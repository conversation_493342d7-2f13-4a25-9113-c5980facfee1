{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/interopRequireDefault.js"], "sourcesContent": ["function _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    \"default\": e\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,uBAAuB,CAAC;IAC/B,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAC7B,WAAW;IACb;AACF;AACA,OAAO,OAAO,GAAG,wBAAwB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/typeof.js"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,QAAQ,CAAC;IAChB;IAEA,OAAO,OAAO,OAAO,GAAG,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,QAAQ,GAAG,SAAU,CAAC;QAC/G,OAAO,OAAO;IAChB,IAAI,SAAU,CAAC;QACb,OAAO,KAAK,cAAc,OAAO,UAAU,EAAE,WAAW,KAAK,UAAU,MAAM,OAAO,SAAS,GAAG,WAAW,OAAO;IACpH,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,QAAQ;AAC3F;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/regeneratorRuntime.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _regeneratorRuntime() {\n  \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */\n  module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return r;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  var t,\n    r = {},\n    e = Object.prototype,\n    n = e.hasOwnProperty,\n    o = \"function\" == typeof Symbol ? Symbol : {},\n    i = o.iterator || \"@@iterator\",\n    a = o.asyncIterator || \"@@asyncIterator\",\n    u = o.toStringTag || \"@@toStringTag\";\n  function c(t, r, e, n) {\n    return Object.defineProperty(t, r, {\n      value: e,\n      enumerable: !n,\n      configurable: !n,\n      writable: !n\n    });\n  }\n  try {\n    c({}, \"\");\n  } catch (t) {\n    c = function c(t, r, e) {\n      return t[r] = e;\n    };\n  }\n  function h(r, e, n, o) {\n    var i = e && e.prototype instanceof Generator ? e : Generator,\n      a = Object.create(i.prototype);\n    return c(a, \"_invoke\", function (r, e, n) {\n      var o = 1;\n      return function (i, a) {\n        if (3 === o) throw Error(\"Generator is already running\");\n        if (4 === o) {\n          if (\"throw\" === i) throw a;\n          return {\n            value: t,\n            done: !0\n          };\n        }\n        for (n.method = i, n.arg = a;;) {\n          var u = n.delegate;\n          if (u) {\n            var c = d(u, n);\n            if (c) {\n              if (c === f) continue;\n              return c;\n            }\n          }\n          if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) {\n            if (1 === o) throw o = 4, n.arg;\n            n.dispatchException(n.arg);\n          } else \"return\" === n.method && n.abrupt(\"return\", n.arg);\n          o = 3;\n          var h = s(r, e, n);\n          if (\"normal\" === h.type) {\n            if (o = n.done ? 4 : 2, h.arg === f) continue;\n            return {\n              value: h.arg,\n              done: n.done\n            };\n          }\n          \"throw\" === h.type && (o = 4, n.method = \"throw\", n.arg = h.arg);\n        }\n      };\n    }(r, n, new Context(o || [])), !0), a;\n  }\n  function s(t, r, e) {\n    try {\n      return {\n        type: \"normal\",\n        arg: t.call(r, e)\n      };\n    } catch (t) {\n      return {\n        type: \"throw\",\n        arg: t\n      };\n    }\n  }\n  r.wrap = h;\n  var f = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var l = {};\n  c(l, i, function () {\n    return this;\n  });\n  var p = Object.getPrototypeOf,\n    y = p && p(p(x([])));\n  y && y !== e && n.call(y, i) && (l = y);\n  var v = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(l);\n  function g(t) {\n    [\"next\", \"throw\", \"return\"].forEach(function (r) {\n      c(t, r, function (t) {\n        return this._invoke(r, t);\n      });\n    });\n  }\n  function AsyncIterator(t, r) {\n    function e(o, i, a, u) {\n      var c = s(t[o], t, i);\n      if (\"throw\" !== c.type) {\n        var h = c.arg,\n          f = h.value;\n        return f && \"object\" == _typeof(f) && n.call(f, \"__await\") ? r.resolve(f.__await).then(function (t) {\n          e(\"next\", t, a, u);\n        }, function (t) {\n          e(\"throw\", t, a, u);\n        }) : r.resolve(f).then(function (t) {\n          h.value = t, a(h);\n        }, function (t) {\n          return e(\"throw\", t, a, u);\n        });\n      }\n      u(c.arg);\n    }\n    var o;\n    c(this, \"_invoke\", function (t, n) {\n      function i() {\n        return new r(function (r, o) {\n          e(t, n, r, o);\n        });\n      }\n      return o = o ? o.then(i, i) : i();\n    }, !0);\n  }\n  function d(r, e) {\n    var n = e.method,\n      o = r.i[n];\n    if (o === t) return e.delegate = null, \"throw\" === n && r.i[\"return\"] && (e.method = \"return\", e.arg = t, d(r, e), \"throw\" === e.method) || \"return\" !== n && (e.method = \"throw\", e.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), f;\n    var i = s(o, r.i, e.arg);\n    if (\"throw\" === i.type) return e.method = \"throw\", e.arg = i.arg, e.delegate = null, f;\n    var a = i.arg;\n    return a ? a.done ? (e[r.r] = a.value, e.next = r.n, \"return\" !== e.method && (e.method = \"next\", e.arg = t), e.delegate = null, f) : a : (e.method = \"throw\", e.arg = new TypeError(\"iterator result is not an object\"), e.delegate = null, f);\n  }\n  function w(t) {\n    this.tryEntries.push(t);\n  }\n  function m(r) {\n    var e = r[4] || {};\n    e.type = \"normal\", e.arg = t, r[4] = e;\n  }\n  function Context(t) {\n    this.tryEntries = [[-1]], t.forEach(w, this), this.reset(!0);\n  }\n  function x(r) {\n    if (null != r) {\n      var e = r[i];\n      if (e) return e.call(r);\n      if (\"function\" == typeof r.next) return r;\n      if (!isNaN(r.length)) {\n        var o = -1,\n          a = function e() {\n            for (; ++o < r.length;) if (n.call(r, o)) return e.value = r[o], e.done = !1, e;\n            return e.value = t, e.done = !0, e;\n          };\n        return a.next = a;\n      }\n    }\n    throw new TypeError(_typeof(r) + \" is not iterable\");\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, c(v, \"constructor\", GeneratorFunctionPrototype), c(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = c(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), r.isGeneratorFunction = function (t) {\n    var r = \"function\" == typeof t && t.constructor;\n    return !!r && (r === GeneratorFunction || \"GeneratorFunction\" === (r.displayName || r.name));\n  }, r.mark = function (t) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, c(t, u, \"GeneratorFunction\")), t.prototype = Object.create(v), t;\n  }, r.awrap = function (t) {\n    return {\n      __await: t\n    };\n  }, g(AsyncIterator.prototype), c(AsyncIterator.prototype, a, function () {\n    return this;\n  }), r.AsyncIterator = AsyncIterator, r.async = function (t, e, n, o, i) {\n    void 0 === i && (i = Promise);\n    var a = new AsyncIterator(h(t, e, n, o), i);\n    return r.isGeneratorFunction(e) ? a : a.next().then(function (t) {\n      return t.done ? t.value : a.next();\n    });\n  }, g(v), c(v, u, \"Generator\"), c(v, i, function () {\n    return this;\n  }), c(v, \"toString\", function () {\n    return \"[object Generator]\";\n  }), r.keys = function (t) {\n    var r = Object(t),\n      e = [];\n    for (var n in r) e.unshift(n);\n    return function t() {\n      for (; e.length;) if ((n = e.pop()) in r) return t.value = n, t.done = !1, t;\n      return t.done = !0, t;\n    };\n  }, r.values = x, Context.prototype = {\n    constructor: Context,\n    reset: function reset(r) {\n      if (this.prev = this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(m), !r) for (var e in this) \"t\" === e.charAt(0) && n.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = t);\n    },\n    stop: function stop() {\n      this.done = !0;\n      var t = this.tryEntries[0][4];\n      if (\"throw\" === t.type) throw t.arg;\n      return this.rval;\n    },\n    dispatchException: function dispatchException(r) {\n      if (this.done) throw r;\n      var e = this;\n      function n(t) {\n        a.type = \"throw\", a.arg = r, e.next = t;\n      }\n      for (var o = e.tryEntries.length - 1; o >= 0; --o) {\n        var i = this.tryEntries[o],\n          a = i[4],\n          u = this.prev,\n          c = i[1],\n          h = i[2];\n        if (-1 === i[0]) return n(\"end\"), !1;\n        if (!c && !h) throw Error(\"try statement without catch or finally\");\n        if (null != i[0] && i[0] <= u) {\n          if (u < c) return this.method = \"next\", this.arg = t, n(c), !0;\n          if (u < h) return n(h), !1;\n        }\n      }\n    },\n    abrupt: function abrupt(t, r) {\n      for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n        var n = this.tryEntries[e];\n        if (n[0] > -1 && n[0] <= this.prev && this.prev < n[2]) {\n          var o = n;\n          break;\n        }\n      }\n      o && (\"break\" === t || \"continue\" === t) && o[0] <= r && r <= o[2] && (o = null);\n      var i = o ? o[4] : {};\n      return i.type = t, i.arg = r, o ? (this.method = \"next\", this.next = o[2], f) : this.complete(i);\n    },\n    complete: function complete(t, r) {\n      if (\"throw\" === t.type) throw t.arg;\n      return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && r && (this.next = r), f;\n    },\n    finish: function finish(t) {\n      for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n        var e = this.tryEntries[r];\n        if (e[2] === t) return this.complete(e[4], e[3]), m(e), f;\n      }\n    },\n    \"catch\": function _catch(t) {\n      for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n        var e = this.tryEntries[r];\n        if (e[0] === t) {\n          var n = e[4];\n          if (\"throw\" === n.type) {\n            var o = n.arg;\n            m(e);\n          }\n          return o;\n        }\n      }\n      throw Error(\"illegal catch attempt\");\n    },\n    delegateYield: function delegateYield(r, e, n) {\n      return this.delegate = {\n        i: x(r),\n        r: e,\n        n: n\n      }, \"next\" === this.method && (this.arg = t), f;\n    }\n  }, r;\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,SAAS;IACP,cAAc,gKAAgK;IAC9K,OAAO,OAAO,GAAG,sBAAsB,SAAS;QAC9C,OAAO;IACT,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO;IAC/E,IAAI,GACF,IAAI,CAAC,GACL,IAAI,OAAO,SAAS,EACpB,IAAI,EAAE,cAAc,EACpB,IAAI,cAAc,OAAO,SAAS,SAAS,CAAC,GAC5C,IAAI,EAAE,QAAQ,IAAI,cAClB,IAAI,EAAE,aAAa,IAAI,mBACvB,IAAI,EAAE,WAAW,IAAI;IACvB,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,OAAO,OAAO,cAAc,CAAC,GAAG,GAAG;YACjC,OAAO;YACP,YAAY,CAAC;YACb,cAAc,CAAC;YACf,UAAU,CAAC;QACb;IACF;IACA,IAAI;QACF,EAAE,CAAC,GAAG;IACR,EAAE,OAAO,GAAG;QACV,IAAI,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC,EAAE,GAAG;QAChB;IACF;IACA,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,IAAI,IAAI,KAAK,EAAE,SAAS,YAAY,YAAY,IAAI,WAClD,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS;QAC/B,OAAO,EAAE,GAAG,WAAW,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;YACtC,IAAI,IAAI;YACR,OAAO,SAAU,CAAC,EAAE,CAAC;gBACnB,IAAI,MAAM,GAAG,MAAM,MAAM;gBACzB,IAAI,MAAM,GAAG;oBACX,IAAI,YAAY,GAAG,MAAM;oBACzB,OAAO;wBACL,OAAO;wBACP,MAAM,CAAC;oBACT;gBACF;gBACA,IAAK,EAAE,MAAM,GAAG,GAAG,EAAE,GAAG,GAAG,IAAK;oBAC9B,IAAI,IAAI,EAAE,QAAQ;oBAClB,IAAI,GAAG;wBACL,IAAI,IAAI,EAAE,GAAG;wBACb,IAAI,GAAG;4BACL,IAAI,MAAM,GAAG;4BACb,OAAO;wBACT;oBACF;oBACA,IAAI,WAAW,EAAE,MAAM,EAAE,EAAE,IAAI,GAAG,EAAE,KAAK,GAAG,EAAE,GAAG;yBAAM,IAAI,YAAY,EAAE,MAAM,EAAE;wBAC/E,IAAI,MAAM,GAAG,MAAM,IAAI,GAAG,EAAE,GAAG;wBAC/B,EAAE,iBAAiB,CAAC,EAAE,GAAG;oBAC3B,OAAO,aAAa,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,UAAU,EAAE,GAAG;oBACxD,IAAI;oBACJ,IAAI,IAAI,EAAE,GAAG,GAAG;oBAChB,IAAI,aAAa,EAAE,IAAI,EAAE;wBACvB,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG;wBACrC,OAAO;4BACL,OAAO,EAAE,GAAG;4BACZ,MAAM,EAAE,IAAI;wBACd;oBACF;oBACA,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,MAAM,GAAG,SAAS,EAAE,GAAG,GAAG,EAAE,GAAG;gBACjE;YACF;QACF,EAAE,GAAG,GAAG,IAAI,QAAQ,KAAK,EAAE,IAAI,CAAC,IAAI;IACtC;IACA,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QAChB,IAAI;YACF,OAAO;gBACL,MAAM;gBACN,KAAK,EAAE,IAAI,CAAC,GAAG;YACjB;QACF,EAAE,OAAO,GAAG;YACV,OAAO;gBACL,MAAM;gBACN,KAAK;YACP;QACF;IACF;IACA,EAAE,IAAI,GAAG;IACT,IAAI,IAAI,CAAC;IACT,SAAS,aAAa;IACtB,SAAS,qBAAqB;IAC9B,SAAS,8BAA8B;IACvC,IAAI,IAAI,CAAC;IACT,EAAE,GAAG,GAAG;QACN,OAAO,IAAI;IACb;IACA,IAAI,IAAI,OAAO,cAAc,EAC3B,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE;IACnB,KAAK,MAAM,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC;IACtC,IAAI,IAAI,2BAA2B,SAAS,GAAG,UAAU,SAAS,GAAG,OAAO,MAAM,CAAC;IACnF,SAAS,EAAE,CAAC;QACV;YAAC;YAAQ;YAAS;SAAS,CAAC,OAAO,CAAC,SAAU,CAAC;YAC7C,EAAE,GAAG,GAAG,SAAU,CAAC;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG;YACzB;QACF;IACF;IACA,SAAS,cAAc,CAAC,EAAE,CAAC;QACzB,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,IAAI,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG;YACnB,IAAI,YAAY,EAAE,IAAI,EAAE;gBACtB,IAAI,IAAI,EAAE,GAAG,EACX,IAAI,EAAE,KAAK;gBACb,OAAO,KAAK,YAAY,QAAQ,MAAM,EAAE,IAAI,CAAC,GAAG,aAAa,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,SAAU,CAAC;oBAChG,EAAE,QAAQ,GAAG,GAAG;gBAClB,GAAG,SAAU,CAAC;oBACZ,EAAE,SAAS,GAAG,GAAG;gBACnB,KAAK,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC;oBAChC,EAAE,KAAK,GAAG,GAAG,EAAE;gBACjB,GAAG,SAAU,CAAC;oBACZ,OAAO,EAAE,SAAS,GAAG,GAAG;gBAC1B;YACF;YACA,EAAE,EAAE,GAAG;QACT;QACA,IAAI;QACJ,EAAE,IAAI,EAAE,WAAW,SAAU,CAAC,EAAE,CAAC;YAC/B,SAAS;gBACP,OAAO,IAAI,EAAE,SAAU,CAAC,EAAE,CAAC;oBACzB,EAAE,GAAG,GAAG,GAAG;gBACb;YACF;YACA,OAAO,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAChC,GAAG,CAAC;IACN;IACA,SAAS,EAAE,CAAC,EAAE,CAAC;QACb,IAAI,IAAI,EAAE,MAAM,EACd,IAAI,EAAE,CAAC,CAAC,EAAE;QACZ,IAAI,MAAM,GAAG,OAAO,EAAE,QAAQ,GAAG,MAAM,YAAY,KAAK,EAAE,CAAC,CAAC,SAAS,IAAI,CAAC,EAAE,MAAM,GAAG,UAAU,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,YAAY,EAAE,MAAM,KAAK,aAAa,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,EAAE,GAAG,GAAG,IAAI,UAAU,sCAAsC,IAAI,WAAW,GAAG;QACjQ,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG;QACvB,IAAI,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,QAAQ,GAAG,MAAM;QACrF,IAAI,IAAI,EAAE,GAAG;QACb,OAAO,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE,aAAa,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,EAAE,GAAG,GAAG,IAAI,UAAU,qCAAqC,EAAE,QAAQ,GAAG,MAAM,CAAC;IAChP;IACA,SAAS,EAAE,CAAC;QACV,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IACvB;IACA,SAAS,EAAE,CAAC;QACV,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;QACjB,EAAE,IAAI,GAAG,UAAU,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG;IACvC;IACA,SAAS,QAAQ,CAAC;QAChB,IAAI,CAAC,UAAU,GAAG;YAAC;gBAAC,CAAC;aAAE;SAAC,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5D;IACA,SAAS,EAAE,CAAC;QACV,IAAI,QAAQ,GAAG;YACb,IAAI,IAAI,CAAC,CAAC,EAAE;YACZ,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;YACrB,IAAI,cAAc,OAAO,EAAE,IAAI,EAAE,OAAO;YACxC,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG;gBACpB,IAAI,IAAI,CAAC,GACP,IAAI,SAAS;oBACX,MAAO,EAAE,IAAI,EAAE,MAAM,EAAG,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,OAAO,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,GAAG,CAAC,GAAG;oBAC9E,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG;gBACnC;gBACF,OAAO,EAAE,IAAI,GAAG;YAClB;QACF;QACA,MAAM,IAAI,UAAU,QAAQ,KAAK;IACnC;IACA,OAAO,kBAAkB,SAAS,GAAG,4BAA4B,EAAE,GAAG,eAAe,6BAA6B,EAAE,4BAA4B,eAAe,oBAAoB,kBAAkB,WAAW,GAAG,EAAE,4BAA4B,GAAG,sBAAsB,EAAE,mBAAmB,GAAG,SAAU,CAAC;QAC3S,IAAI,IAAI,cAAc,OAAO,KAAK,EAAE,WAAW;QAC/C,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,qBAAqB,wBAAwB,CAAC,EAAE,WAAW,IAAI,EAAE,IAAI,CAAC;IAC7F,GAAG,EAAE,IAAI,GAAG,SAAU,CAAC;QACrB,OAAO,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,GAAG,8BAA8B,CAAC,EAAE,SAAS,GAAG,4BAA4B,EAAE,GAAG,GAAG,oBAAoB,GAAG,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,IAAI;IAClM,GAAG,EAAE,KAAK,GAAG,SAAU,CAAC;QACtB,OAAO;YACL,SAAS;QACX;IACF,GAAG,EAAE,cAAc,SAAS,GAAG,EAAE,cAAc,SAAS,EAAE,GAAG;QAC3D,OAAO,IAAI;IACb,IAAI,EAAE,aAAa,GAAG,eAAe,EAAE,KAAK,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACpE,KAAK,MAAM,KAAK,CAAC,IAAI,OAAO;QAC5B,IAAI,IAAI,IAAI,cAAc,EAAE,GAAG,GAAG,GAAG,IAAI;QACzC,OAAO,EAAE,mBAAmB,CAAC,KAAK,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,SAAU,CAAC;YAC7D,OAAO,EAAE,IAAI,GAAG,EAAE,KAAK,GAAG,EAAE,IAAI;QAClC;IACF,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,cAAc,EAAE,GAAG,GAAG;QACrC,OAAO,IAAI;IACb,IAAI,EAAE,GAAG,YAAY;QACnB,OAAO;IACT,IAAI,EAAE,IAAI,GAAG,SAAU,CAAC;QACtB,IAAI,IAAI,OAAO,IACb,IAAI,EAAE;QACR,IAAK,IAAI,KAAK,EAAG,EAAE,OAAO,CAAC;QAC3B,OAAO,SAAS;YACd,MAAO,EAAE,MAAM,EAAG,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG;YAC3E,OAAO,EAAE,IAAI,GAAG,CAAC,GAAG;QACtB;IACF,GAAG,EAAE,MAAM,GAAG,GAAG,QAAQ,SAAS,GAAG;QACnC,aAAa;QACb,OAAO,SAAS,MAAM,CAAC;YACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAK,IAAI,KAAK,IAAI,CAAE,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QACxQ;QACA,MAAM,SAAS;YACb,IAAI,CAAC,IAAI,GAAG,CAAC;YACb,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;YAC7B,IAAI,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;YACnC,OAAO,IAAI,CAAC,IAAI;QAClB;QACA,mBAAmB,SAAS,kBAAkB,CAAC;YAC7C,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM;YACrB,IAAI,IAAI,IAAI;YACZ,SAAS,EAAE,CAAC;gBACV,EAAE,IAAI,GAAG,SAAS,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG;YACxC;YACA,IAAK,IAAI,IAAI,EAAE,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACjD,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,EACxB,IAAI,CAAC,CAAC,EAAE,EACR,IAAI,IAAI,CAAC,IAAI,EACb,IAAI,CAAC,CAAC,EAAE,EACR,IAAI,CAAC,CAAC,EAAE;gBACV,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC;gBACnC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,MAAM;gBAC1B,IAAI,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,GAAG;oBAC7B,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC;oBAC7D,IAAI,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;gBAC3B;YACF;QACF;QACA,QAAQ,SAAS,OAAO,CAAC,EAAE,CAAC;YAC1B,IAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACpD,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC1B,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE;oBACtD,IAAI,IAAI;oBACR;gBACF;YACF;YACA,KAAK,CAAC,YAAY,KAAK,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI;YAC/E,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;YACpB,OAAO,EAAE,IAAI,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC;QAChG;QACA,UAAU,SAAS,SAAS,CAAC,EAAE,CAAC;YAC9B,IAAI,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;YACnC,OAAO,YAAY,EAAE,IAAI,IAAI,eAAe,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,GAAG,aAAa,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,aAAa,EAAE,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG;QAC1N;QACA,QAAQ,SAAS,OAAO,CAAC;YACvB,IAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACpD,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC1B,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI;YAC1D;QACF;QACA,SAAS,SAAS,OAAO,CAAC;YACxB,IAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACpD,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC1B,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG;oBACd,IAAI,IAAI,CAAC,CAAC,EAAE;oBACZ,IAAI,YAAY,EAAE,IAAI,EAAE;wBACtB,IAAI,IAAI,EAAE,GAAG;wBACb,EAAE;oBACJ;oBACA,OAAO;gBACT;YACF;YACA,MAAM,MAAM;QACd;QACA,eAAe,SAAS,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,QAAQ,GAAG;gBACrB,GAAG,EAAE;gBACL,GAAG;gBACH,GAAG;YACL,GAAG,WAAW,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG;QAC/C;IACF,GAAG;AACL;AACA,OAAO,OAAO,GAAG,qBAAqB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/regenerator/index.js"], "sourcesContent": ["// TODO(Babel 8): Remove this file.\n\nvar runtime = require(\"../helpers/regeneratorRuntime\")();\nmodule.exports = runtime;\n\n// Copied from https://github.com/facebook/regenerator/blob/main/packages/runtime/runtime.js#L736=\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,IAAI,UAAU;AACd,OAAO,OAAO,GAAG;AAEjB,kGAAkG;AAClG,IAAI;IACF,qBAAqB;AACvB,EAAE,OAAO,sBAAsB;IAC7B,IAAI,OAAO,eAAe,UAAU;QAClC,WAAW,kBAAkB,GAAG;IAClC,OAAO;QACL,SAAS,KAAK,0BAA0B;IAC1C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/toPrimitive.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,SAAS,YAAY,CAAC,EAAE,CAAC;IACvB,IAAI,YAAY,QAAQ,MAAM,CAAC,GAAG,OAAO;IACzC,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAC7B,IAAI,KAAK,MAAM,GAAG;QAChB,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QACvB,IAAI,YAAY,QAAQ,IAAI,OAAO;QACnC,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAC5C;AACA,OAAO,OAAO,GAAG,aAAa,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/toPropertyKey.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,IAAI;AACJ,SAAS,cAAc,CAAC;IACtB,IAAI,IAAI,YAAY,GAAG;IACvB,OAAO,YAAY,QAAQ,KAAK,IAAI,IAAI;AAC1C;AACA,OAAO,OAAO,GAAG,eAAe,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/defineProperty.js"], "sourcesContent": ["var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,OAAO,CAAC,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAC/D,OAAO;QACP,YAAY,CAAC;QACb,cAAc,CAAC;QACf,UAAU,CAAC;IACb,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AACjB;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/asyncToGenerator.js"], "sourcesContent": ["function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nmodule.exports = _asyncToGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,mBAAmB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7C,IAAI;QACF,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IACX,IAAI,EAAE,KAAK;IACf,EAAE,OAAO,GAAG;QACV,OAAO,KAAK,EAAE;IAChB;IACA,EAAE,IAAI,GAAG,EAAE,KAAK,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;AAC7C;AACA,SAAS,kBAAkB,CAAC;IAC1B,OAAO;QACL,IAAI,IAAI,IAAI,EACV,IAAI;QACN,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,EAAE,KAAK,CAAC,GAAG;YACnB,SAAS,MAAM,CAAC;gBACd,mBAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,QAAQ;YACrD;YACA,SAAS,OAAO,CAAC;gBACf,mBAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,SAAS;YACtD;YACA,MAAM,KAAK;QACb;IACF;AACF;AACA,OAAO,OAAO,GAAG,mBAAmB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/arrayWithHoles.js"], "sourcesContent": ["function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nmodule.exports = _arrayWithHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC;IACxB,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO;AAC/B;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/iterableToArrayLimit.js"], "sourcesContent": ["function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nmodule.exports = _iterableToArrayLimit, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,sBAAsB,CAAC,EAAE,CAAC;IACjC,IAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa;IAChG,IAAI,QAAQ,GAAG;QACb,IAAI,GACF,GACA,GACA,GACA,IAAI,EAAE,EACN,IAAI,CAAC,GACL,IAAI,CAAC;QACP,IAAI;YACF,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,GAAG;gBACrC,IAAI,OAAO,OAAO,GAAG;gBACrB,IAAI,CAAC;YACP,OAAO,MAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,EAAE,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;QACvF,EAAE,OAAO,GAAG;YACV,IAAI,CAAC,GAAG,IAAI;QACd,SAAU;YACR,IAAI;gBACF,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG;YACzE,SAAU;gBACR,IAAI,GAAG,MAAM;YACf;QACF;QACA,OAAO;IACT;AACF;AACA,OAAO,OAAO,GAAG,uBAAuB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/arrayLikeToArray.js"], "sourcesContent": ["function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nmodule.exports = _arrayLikeToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC7B,CAAC,QAAQ,KAAK,IAAI,EAAE,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACrD,OAAO;AACT;AACA,OAAO,OAAO,GAAG,mBAAmB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/unsupportedIterableToArray.js"], "sourcesContent": ["var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nmodule.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,4BAA4B,CAAC,EAAE,CAAC;IACvC,IAAI,GAAG;QACL,IAAI,YAAY,OAAO,GAAG,OAAO,iBAAiB,GAAG;QACrD,IAAI,IAAI,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;QACtC,OAAO,aAAa,KAAK,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,GAAG,UAAU,KAAK,UAAU,IAAI,MAAM,IAAI,CAAC,KAAK,gBAAgB,KAAK,2CAA2C,IAAI,CAAC,KAAK,iBAAiB,GAAG,KAAK,KAAK;IAC3N;AACF;AACA,OAAO,OAAO,GAAG,6BAA6B,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/nonIterableRest.js"], "sourcesContent": ["function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableRest, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB;AACA,OAAO,OAAO,GAAG,kBAAkB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/slicedToArray.js"], "sourcesContent": ["var arrayWithHoles = require(\"./arrayWithHoles.js\");\nvar iterableToArrayLimit = require(\"./iterableToArrayLimit.js\");\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\nvar nonIterableRest = require(\"./nonIterableRest.js\");\nfunction _slicedToArray(r, e) {\n  return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();\n}\nmodule.exports = _slicedToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,OAAO,eAAe,MAAM,qBAAqB,GAAG,MAAM,2BAA2B,GAAG,MAAM;AAChG;AACA,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/classCallCheck.js"], "sourcesContent": ["function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,IAAI,CAAC,CAAC,aAAa,CAAC,GAAG,MAAM,IAAI,UAAU;AAC7C;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/createClass.js"], "sourcesContent": ["var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;QACjC,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,EAAE,UAAU,GAAG,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,YAAY,GAAG,CAAC,GAAG,WAAW,KAAK,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,cAAc,CAAC,GAAG,cAAc,EAAE,GAAG,GAAG;IAC5I;AACF;AACA,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,OAAO,KAAK,kBAAkB,EAAE,SAAS,EAAE,IAAI,KAAK,kBAAkB,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,aAAa;QACjH,UAAU,CAAC;IACb,IAAI;AACN;AACA,OAAO,OAAO,GAAG,cAAc,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/assertThisInitialized.js"], "sourcesContent": ["function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nmodule.exports = _assertThisInitialized, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,uBAAuB,CAAC;IAC/B,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,eAAe;IAC3C,OAAO;AACT;AACA,OAAO,OAAO,GAAG,wBAAwB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/possibleConstructorReturn.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,IAAI;AACJ,SAAS,2BAA2B,CAAC,EAAE,CAAC;IACtC,IAAI,KAAK,CAAC,YAAY,QAAQ,MAAM,cAAc,OAAO,CAAC,GAAG,OAAO;IACpE,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,UAAU;IACtC,OAAO,sBAAsB;AAC/B;AACA,OAAO,OAAO,GAAG,4BAA4B,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/getPrototypeOf.js"], "sourcesContent": ["function _getPrototypeOf(t) {\n  return module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _getPrototypeOf(t);\n}\nmodule.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC;IACxB,OAAO,OAAO,OAAO,GAAG,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC;QAC1G,OAAO,EAAE,SAAS,IAAI,OAAO,cAAc,CAAC;IAC9C,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,gBAAgB;AACnG;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/setPrototypeOf.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _setPrototypeOf(t, e);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,OAAO,OAAO,OAAO,GAAG,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;QAC7G,OAAO,EAAE,SAAS,GAAG,GAAG;IAC1B,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,gBAAgB,GAAG;AACtG;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/inherits.js"], "sourcesContent": ["var setPrototypeOf = require(\"./setPrototypeOf.js\");\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nmodule.exports = _inherits, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,IAAI,cAAc,OAAO,KAAK,SAAS,GAAG,MAAM,IAAI,UAAU;IAC9D,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,KAAK,EAAE,SAAS,EAAE;QAC5C,aAAa;YACX,OAAO;YACP,UAAU,CAAC;YACX,cAAc,CAAC;QACjB;IACF,IAAI,OAAO,cAAc,CAAC,GAAG,aAAa;QACxC,UAAU,CAAC;IACb,IAAI,KAAK,eAAe,GAAG;AAC7B;AACA,OAAO,OAAO,GAAG,WAAW,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/isNativeFunction.js"], "sourcesContent": ["function _isNativeFunction(t) {\n  try {\n    return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n  } catch (n) {\n    return \"function\" == typeof t;\n  }\n}\nmodule.exports = _isNativeFunction, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,kBAAkB,CAAC;IAC1B,IAAI;QACF,OAAO,CAAC,MAAM,SAAS,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;IAClD,EAAE,OAAO,GAAG;QACV,OAAO,cAAc,OAAO;IAC9B;AACF;AACA,OAAO,OAAO,GAAG,mBAAmB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/isNativeReflectConstruct.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (module.exports = _isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS;IACP,IAAI;QACF,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,SAAS,EAAE,EAAE,YAAa;IACtF,EAAE,OAAO,GAAG,CAAC;IACb,OAAO,CAAC,OAAO,OAAO,GAAG,4BAA4B,SAAS;QAC5D,OAAO,CAAC,CAAC;IACX,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO;AACjF;AACA,OAAO,OAAO,GAAG,2BAA2B,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/construct.js"], "sourcesContent": ["var isNativeReflectConstruct = require(\"./isNativeReflectConstruct.js\");\nvar setPrototypeOf = require(\"./setPrototypeOf.js\");\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nmodule.exports = _construct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;IACzB,IAAI,4BAA4B,OAAO,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM;IACrE,IAAI,IAAI;QAAC;KAAK;IACd,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAChB,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;IAC/B,OAAO,KAAK,eAAe,GAAG,EAAE,SAAS,GAAG;AAC9C;AACA,OAAO,OAAO,GAAG,YAAY,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40babel/runtime/helpers/wrapNativeSuper.js"], "sourcesContent": ["var getPrototypeOf = require(\"./getPrototypeOf.js\");\nvar setPrototypeOf = require(\"./setPrototypeOf.js\");\nvar isNativeFunction = require(\"./isNativeFunction.js\");\nvar construct = require(\"./construct.js\");\nfunction _wrapNativeSuper(t) {\n  var r = \"function\" == typeof Map ? new Map() : void 0;\n  return module.exports = _wrapNativeSuper = function _wrapNativeSuper(t) {\n    if (null === t || !isNativeFunction(t)) return t;\n    if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n    if (void 0 !== r) {\n      if (r.has(t)) return r.get(t);\n      r.set(t, Wrapper);\n    }\n    function Wrapper() {\n      return construct(t, arguments, getPrototypeOf(this).constructor);\n    }\n    return Wrapper.prototype = Object.create(t.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: !1,\n        writable: !0,\n        configurable: !0\n      }\n    }), setPrototypeOf(Wrap<PERSON>, t);\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _wrapNativeSuper(t);\n}\nmodule.exports = _wrapNativeSuper, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,iBAAiB,CAAC;IACzB,IAAI,IAAI,cAAc,OAAO,MAAM,IAAI,QAAQ,KAAK;IACpD,OAAO,OAAO,OAAO,GAAG,mBAAmB,SAAS,iBAAiB,CAAC;QACpE,IAAI,SAAS,KAAK,CAAC,iBAAiB,IAAI,OAAO;QAC/C,IAAI,cAAc,OAAO,GAAG,MAAM,IAAI,UAAU;QAChD,IAAI,KAAK,MAAM,GAAG;YAChB,IAAI,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;YAC3B,EAAE,GAAG,CAAC,GAAG;QACX;QACA,SAAS;YACP,OAAO,UAAU,GAAG,WAAW,eAAe,IAAI,EAAE,WAAW;QACjE;QACA,OAAO,QAAQ,SAAS,GAAG,OAAO,MAAM,CAAC,EAAE,SAAS,EAAE;YACpD,aAAa;gBACX,OAAO;gBACP,YAAY,CAAC;gBACb,UAAU,CAAC;gBACX,cAAc,CAAC;YACjB;QACF,IAAI,eAAe,SAAS;IAC9B,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,iBAAiB;AACpG;AACA,OAAO,OAAO,GAAG,kBAAkB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next-auth/core/errors.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/wrapNativeSuper\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar UnknownError = exports.UnknownError = function (_Error) {\n  function UnknownError(error) {\n    var _message;\n    var _this;\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n    return _this;\n  }\n  (0, _inherits2.default)(UnknownError, _Error);\n  return (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n}((0, _wrapNativeSuper2.default)(Error));\nvar OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {\n  function OAuthCallbackError() {\n    var _this2;\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));\n    (0, _defineProperty2.default)(_this2, \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nvar AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {\n  function AccountNotLinkedError() {\n    var _this3;\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));\n    (0, _defineProperty2.default)(_this3, \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nvar MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {\n  function MissingAPIRoute() {\n    var _this4;\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));\n    (0, _defineProperty2.default)(_this4, \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)(_this4, \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nvar MissingSecret = exports.MissingSecret = function (_UnknownError4) {\n  function MissingSecret() {\n    var _this5;\n    (0, _classCallCheck2.default)(this, MissingSecret);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, MissingSecret, [].concat(args));\n    (0, _defineProperty2.default)(_this5, \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)(_this5, \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nvar MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {\n  function MissingAuthorize() {\n    var _this6;\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));\n    (0, _defineProperty2.default)(_this6, \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)(_this6, \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nvar MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {\n  function MissingAdapter() {\n    var _this7;\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, MissingAdapter, [].concat(args));\n    (0, _defineProperty2.default)(_this7, \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)(_this7, \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nvar MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {\n  function MissingAdapterMethods() {\n    var _this8;\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));\n    (0, _defineProperty2.default)(_this8, \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)(_this8, \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nvar UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {\n  function UnsupportedStrategy() {\n    var _this9;\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));\n    (0, _defineProperty2.default)(_this9, \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)(_this9, \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nvar InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {\n  function InvalidCallbackUrl() {\n    var _this10;\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));\n    (0, _defineProperty2.default)(_this10, \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)(_this10, \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.prev = 0;\n            method = methods[name];\n            _context.next = 4;\n            return method.apply(void 0, _args);\n          case 4:\n            return _context.abrupt(\"return\", _context.sent);\n          case 7:\n            _context.prev = 7;\n            _context.t0 = _context[\"catch\"](0);\n            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n        args,\n        _key10,\n        method,\n        e,\n        _args2 = arguments;\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.prev = 0;\n            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = _args2[_key10];\n            }\n            logger.debug(\"adapter_\".concat(name), {\n              args: args\n            });\n            method = adapter[name];\n            _context2.next = 6;\n            return method.apply(void 0, args);\n          case 6:\n            return _context2.abrupt(\"return\", _context2.sent);\n          case 9:\n            _context2.prev = 9;\n            _context2.t0 = _context2[\"catch\"](0);\n            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n            e = new UnknownError(_context2.t0);\n            e.name = \"\".concat(capitalize(name), \"Error\");\n            throw e;\n          case 15:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,mBAAmB,GAAG,QAAQ,YAAY,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,aAAa,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,cAAc,GAAG,QAAQ,eAAe,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,qBAAqB,GAAG,KAAK;AAC1R,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,UAAU,GAAG;AACrB,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,UAAU,GAAG;AACrB,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AACpB,IAAI,8BAA8B;AAClC,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,CAAC,GAAG,4BAA4B,OAAO,EAAE,GAAG,8BAA8B,QAAQ,SAAS,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,WAAW,IAAI,EAAE,KAAK,CAAC,GAAG;AAAK;AACpP,SAAS;IAA8B,IAAI;QAAE,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,SAAS,EAAE,EAAE,YAAa;IAAK,EAAE,OAAO,GAAG,CAAC;IAAE,OAAO,CAAC,4BAA4B,SAAS;QAA8B,OAAO,CAAC,CAAC;IAAG,CAAC;AAAK;AAClP,IAAI,eAAe,QAAQ,YAAY,GAAG,SAAU,MAAM;IACxD,SAAS,aAAa,KAAK;QACzB,IAAI;QACJ,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,QAAQ,WAAW,IAAI,EAAE,cAAc;YAAC,CAAC,WAAW,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,MAAM,QAAQ,aAAa,KAAK,IAAI,WAAW;SAAM;QACpK,MAAM,IAAI,GAAG;QACb,MAAM,IAAI,GAAG,MAAM,IAAI;QACvB,IAAI,iBAAiB,OAAO;YAC1B,MAAM,KAAK,GAAG,MAAM,KAAK;QAC3B;QACA,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,cAAc;IACtC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE,cAAc;QAAC;YAC/C,KAAK;YACL,OAAO,SAAS;gBACd,OAAO;oBACL,MAAM,IAAI,CAAC,IAAI;oBACf,SAAS,IAAI,CAAC,OAAO;oBACrB,OAAO,IAAI,CAAC,KAAK;gBACnB;YACF;QACF;KAAE;AACJ,EAAE,CAAC,GAAG,kBAAkB,OAAO,EAAE;AACjC,IAAI,qBAAqB,QAAQ,kBAAkB,GAAG,SAAU,aAAa;IAC3E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QACA,SAAS,WAAW,IAAI,EAAE,oBAAoB,EAAE,CAAC,MAAM,CAAC;QACxD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,oBAAoB;IAC5C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,wBAAwB,QAAQ,qBAAqB,GAAG,SAAU,cAAc;IAClF,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,uBAAuB,EAAE,CAAC,MAAM,CAAC;QAC3D,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,uBAAuB;IAC/C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,kBAAkB,QAAQ,eAAe,GAAG,SAAU,cAAc;IACtE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,iBAAiB,EAAE,CAAC,MAAM,CAAC;QACrD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,iBAAiB;IACzC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,gBAAgB,QAAQ,aAAa,GAAG,SAAU,cAAc;IAClE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,eAAe,EAAE,CAAC,MAAM,CAAC;QACnD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,eAAe;IACvC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,mBAAmB,QAAQ,gBAAgB,GAAG,SAAU,cAAc;IACxE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,kBAAkB,EAAE,CAAC,MAAM,CAAC;QACtD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,kBAAkB;IAC1C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,iBAAiB,QAAQ,cAAc,GAAG,SAAU,cAAc;IACpE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,gBAAgB,EAAE,CAAC,MAAM,CAAC;QACpD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,gBAAgB;IACxC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,wBAAwB,QAAQ,qBAAqB,GAAG,SAAU,cAAc;IAClF,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,uBAAuB,EAAE,CAAC,MAAM,CAAC;QAC3D,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,uBAAuB;IAC/C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,sBAAsB,QAAQ,mBAAmB,GAAG,SAAU,cAAc;IAC9E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,qBAAqB,EAAE,CAAC,MAAM,CAAC;QACzD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,qBAAqB;IAC7C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,qBAAqB,QAAQ,kBAAkB,GAAG,SAAU,cAAc;IAC5E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,UAAU,WAAW,IAAI,EAAE,oBAAoB,EAAE,CAAC,MAAM,CAAC;QACzD,CAAC,GAAG,iBAAiB,OAAO,EAAE,SAAS,QAAQ;QAC/C,CAAC,GAAG,iBAAiB,OAAO,EAAE,SAAS,QAAQ;QAC/C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,oBAAoB;IAC5C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,SAAS,WAAW,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC,YAAY,OAAO,WAAW;AACjD;AACA,SAAS,WAAW,CAAC;IACnB,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;AACtD;AACA,SAAS,mBAAmB,OAAO,EAAE,MAAM;IACzC,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACpD,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;YAC7E,IAAI,QACF,QAAQ;YACV,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;gBACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;oBAC7C,KAAK;wBACH,SAAS,IAAI,GAAG;wBAChB,SAAS,OAAO,CAAC,KAAK;wBACtB,SAAS,IAAI,GAAG;wBAChB,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG;oBAC9B,KAAK;wBACH,OAAO,SAAS,MAAM,CAAC,UAAU,SAAS,IAAI;oBAChD,KAAK;wBACH,SAAS,IAAI,GAAG;wBAChB,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC;wBAChC,OAAO,KAAK,CAAC,GAAG,MAAM,CAAC,WAAW,OAAO,iBAAiB,SAAS,EAAE;oBACvE,KAAK;oBACL,KAAK;wBACH,OAAO,SAAS,IAAI;gBACxB;YACF,GAAG,SAAS,MAAM;gBAAC;oBAAC;oBAAG;iBAAE;aAAC;QAC5B;QACA,OAAO;IACT,GAAG,CAAC;AACN;AACA,SAAS,oBAAoB,OAAO,EAAE,MAAM;IAC1C,IAAI,CAAC,SAAS;IACd,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACpD,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;YAC7E,IAAI,QACF,MACA,QACA,QACA,GACA,SAAS;YACX,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;gBAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;oBAC/C,KAAK;wBACH,UAAU,IAAI,GAAG;wBACjB,IAAK,SAAS,OAAO,MAAM,EAAE,OAAO,IAAI,MAAM,SAAS,SAAS,GAAG,SAAS,QAAQ,SAAU;4BAC5F,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;wBAC/B;wBACA,OAAO,KAAK,CAAC,WAAW,MAAM,CAAC,OAAO;4BACpC,MAAM;wBACR;wBACA,SAAS,OAAO,CAAC,KAAK;wBACtB,UAAU,IAAI,GAAG;wBACjB,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG;oBAC9B,KAAK;wBACH,OAAO,UAAU,MAAM,CAAC,UAAU,UAAU,IAAI;oBAClD,KAAK;wBACH,UAAU,IAAI,GAAG;wBACjB,UAAU,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC;wBAClC,OAAO,KAAK,CAAC,iBAAiB,MAAM,CAAC,OAAO,UAAU,EAAE;wBACxD,IAAI,IAAI,aAAa,UAAU,EAAE;wBACjC,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,OAAO;wBACrC,MAAM;oBACR,KAAK;oBACL,KAAK;wBACH,OAAO,UAAU,IAAI;gBACzB;YACF,GAAG,UAAU,MAAM;gBAAC;oBAAC;oBAAG;iBAAE;aAAC;QAC7B;QACA,OAAO;IACT,GAAG,CAAC;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next-auth/utils/logger.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _errors = require(\"../core/errors\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n  if (hasErrorProperty(o)) {\n    var _o$message;\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n  return o;\n}\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = exports.default = _logger;\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n  try {\n    if (typeof window === \"undefined\") {\n      return logger;\n    }\n    var clientLogger = {};\n    var _loop = function _loop(level) {\n      clientLogger[level] = function () {\n        var _ref = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(code, metadata) {\n          var url, body;\n          return _regenerator.default.wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                  metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                url = \"\".concat(basePath, \"/_log\");\n                body = new URLSearchParams(_objectSpread({\n                  level: level,\n                  code: code\n                }, metadata));\n                if (!navigator.sendBeacon) {\n                  _context.next = 8;\n                  break;\n                }\n                return _context.abrupt(\"return\", navigator.sendBeacon(url, body));\n              case 8:\n                _context.next = 10;\n                return fetch(url, {\n                  method: \"POST\",\n                  body: body,\n                  keepalive: true\n                });\n              case 10:\n                return _context.abrupt(\"return\", _context.sent);\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    };\n    for (var level in logger) {\n      _loop(level);\n    }\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG;AACpB,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI;AACJ,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACpc,SAAS,YAAY,CAAC;IACpB,IAAI,aAAa,SAAS,CAAC,CAAC,aAAa,QAAQ,YAAY,GAAG;QAC9D,OAAO;YACL,SAAS,EAAE,OAAO;YAClB,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,IAAI;QACd;IACF;IACA,IAAI,iBAAiB,IAAI;QACvB,IAAI;QACJ,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK;QAC7B,EAAE,OAAO,GAAG,CAAC,aAAa,EAAE,OAAO,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa,EAAE,KAAK,CAAC,OAAO;IACvG;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,CAAC;IACzB,OAAO,CAAC,CAAC,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,EAAE,KAAK;AACjD;AACA,IAAI,UAAU;IACZ,OAAO,SAAS,MAAM,IAAI,EAAE,QAAQ;QAClC,WAAW,YAAY;QACvB,QAAQ,KAAK,CAAC,sBAAsB,MAAM,CAAC,MAAM,MAAM,qCAAqC,MAAM,CAAC,KAAK,WAAW,KAAK,SAAS,OAAO,EAAE;IAC5I;IACA,MAAM,SAAS,KAAK,IAAI;QACtB,QAAQ,IAAI,CAAC,qBAAqB,MAAM,CAAC,MAAM,MAAM,uCAAuC,MAAM,CAAC,KAAK,WAAW;IACrH;IACA,OAAO,SAAS,MAAM,IAAI,EAAE,QAAQ;QAClC,QAAQ,GAAG,CAAC,sBAAsB,MAAM,CAAC,MAAM,MAAM;IACvD;AACF;AACA,SAAS;IACP,IAAI,YAAY,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;IACrF,IAAI,QAAQ,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IAClD,IAAI,CAAC,OAAO,QAAQ,KAAK,GAAG,YAAa;IACzC,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;IACpD,IAAI,UAAU,IAAI,EAAE,QAAQ,IAAI,GAAG,UAAU,IAAI;IACjD,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;AACtD;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG;AACjC,SAAS;IACP,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACjF,IAAI,WAAW,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IACrD,IAAI;QACF,IAAI,OAAO,WAAW,aAAa;YACjC,OAAO;QACT;QACA,IAAI,eAAe,CAAC;QACpB,IAAI,QAAQ,SAAS,MAAM,KAAK;YAC9B,YAAY,CAAC,MAAM,GAAG;gBACpB,IAAI,OAAO,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,QAAQ,IAAI,EAAE,QAAQ;oBAClG,IAAI,KAAK;oBACT,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;wBACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;4BAC7C,KAAK;gCACH,OAAO,CAAC,MAAM,CAAC,MAAM;gCACrB,IAAI,UAAU,SAAS;oCACrB,WAAW,YAAY;gCACzB;;gCAEA,SAAS,MAAM,GAAG;gCAClB,MAAM,GAAG,MAAM,CAAC,UAAU;gCAC1B,OAAO,IAAI,gBAAgB,cAAc;oCACvC,OAAO;oCACP,MAAM;gCACR,GAAG;gCACH,IAAI,CAAC,UAAU,UAAU,EAAE;oCACzB,SAAS,IAAI,GAAG;oCAChB;gCACF;gCACA,OAAO,SAAS,MAAM,CAAC,UAAU,UAAU,UAAU,CAAC,KAAK;4BAC7D,KAAK;gCACH,SAAS,IAAI,GAAG;gCAChB,OAAO,MAAM,KAAK;oCAChB,QAAQ;oCACR,MAAM;oCACN,WAAW;gCACb;4BACF,KAAK;gCACH,OAAO,SAAS,MAAM,CAAC,UAAU,SAAS,IAAI;4BAChD,KAAK;4BACL,KAAK;gCACH,OAAO,SAAS,IAAI;wBACxB;oBACF,GAAG;gBACL;gBACA,OAAO,SAAU,EAAE,EAAE,GAAG;oBACtB,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;gBAC1B;YACF;QACF;QACA,IAAK,IAAI,SAAS,OAAQ;YACxB,MAAM;QACR;QACA,OAAO;IACT,EAAE,OAAO,SAAS;QAChB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next-auth/utils/parse-url.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = parseUrl;\nfunction parseUrl(url) {\n  var _url2;\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,SAAS,GAAG;IACnB,IAAI;IACJ,MAAM,aAAa,IAAI,IAAI;IAC3B,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS;QAClC,MAAM,CAAC,QAAQ,EAAE,KAAK;IACxB;IACA,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ,UAAU,KAAK,IAAI,QAAQ;IAC1E,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,MAAM,WAAW,QAAQ,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC,OAAO;IAC1F,MAAM,OAAO,GAAG,KAAK,MAAM,GAAG,MAAM;IACpC,OAAO;QACL,QAAQ,KAAK,MAAM;QACnB,MAAM,KAAK,IAAI;QACf;QACA;QACA,UAAU,IAAM;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next-auth/client/_utils.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.BroadcastChannel = BroadcastChannel;\nexports.apiBaseUrl = apiBaseUrl;\nexports.fetchData = fetchData;\nexports.now = now;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction fetchData(_x, _x2, _x3) {\n  return _fetchData.apply(this, arguments);\n}\nfunction _fetchData() {\n  _fetchData = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(path, __NEXTAUTH, logger) {\n    var _ref,\n      ctx,\n      _ref$req,\n      req,\n      url,\n      _req$headers,\n      options,\n      res,\n      data,\n      _args = arguments;\n    return _regenerator.default.wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _ref = _args.length > 3 && _args[3] !== undefined ? _args[3] : {}, ctx = _ref.ctx, _ref$req = _ref.req, req = _ref$req === void 0 ? ctx === null || ctx === void 0 ? void 0 : ctx.req : _ref$req;\n          url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n          _context.prev = 2;\n          options = {\n            headers: _objectSpread({\n              \"Content-Type\": \"application/json\"\n            }, req !== null && req !== void 0 && (_req$headers = req.headers) !== null && _req$headers !== void 0 && _req$headers.cookie ? {\n              cookie: req.headers.cookie\n            } : {})\n          };\n          if (req !== null && req !== void 0 && req.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n          }\n          _context.next = 7;\n          return fetch(url, options);\n        case 7:\n          res = _context.sent;\n          _context.next = 10;\n          return res.json();\n        case 10:\n          data = _context.sent;\n          if (res.ok) {\n            _context.next = 13;\n            break;\n          }\n          throw data;\n        case 13:\n          return _context.abrupt(\"return\", Object.keys(data).length > 0 ? data : null);\n        case 16:\n          _context.prev = 16;\n          _context.t0 = _context[\"catch\"](2);\n          logger.error(\"CLIENT_FETCH_ERROR\", {\n            error: _context.t0,\n            url: url\n          });\n          return _context.abrupt(\"return\", null);\n        case 20:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee, null, [[2, 16]]);\n  }));\n  return _fetchData.apply(this, arguments);\n}\nfunction apiBaseUrl(__NEXTAUTH) {\n  if (typeof window === \"undefined\") {\n    return \"\".concat(__NEXTAUTH.baseUrlServer).concat(__NEXTAUTH.basePathServer);\n  }\n  return __NEXTAUTH.basePath;\n}\nfunction now() {\n  return Math.floor(Date.now() / 1000);\n}\nfunction BroadcastChannel() {\n  var name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"nextauth.message\";\n  return {\n    receive: function receive(onReceive) {\n      var handler = function handler(event) {\n        var _event$newValue;\n        if (event.key !== name) return;\n        var message = JSON.parse((_event$newValue = event.newValue) !== null && _event$newValue !== void 0 ? _event$newValue : \"{}\");\n        if ((message === null || message === void 0 ? void 0 : message.event) !== \"session\" || !(message !== null && message !== void 0 && message.data)) return;\n        onReceive(message);\n      };\n      window.addEventListener(\"storage\", handler);\n      return function () {\n        return window.removeEventListener(\"storage\", handler);\n      };\n    },\n    post: function post(message) {\n      if (typeof window === \"undefined\") return;\n      try {\n        localStorage.setItem(name, JSON.stringify(_objectSpread(_objectSpread({}, message), {}, {\n          timestamp: now()\n        })));\n      } catch (_unused) {}\n    }\n  };\n}"], "names": [], "mappings": "AAAA;AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,UAAU,GAAG;AACrB,QAAQ,SAAS,GAAG;AACpB,QAAQ,GAAG,GAAG;AACd,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACpc,SAAS,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG;IAC7B,OAAO,WAAW,KAAK,CAAC,IAAI,EAAE;AAChC;AACA,SAAS;IACP,aAAa,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,QAAQ,IAAI,EAAE,UAAU,EAAE,MAAM;QAC9G,IAAI,MACF,KACA,UACA,KACA,KACA,cACA,SACA,KACA,MACA,QAAQ;QACV,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;YACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;gBAC7C,KAAK;oBACH,OAAO,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,EAAE,WAAW,KAAK,GAAG,EAAE,MAAM,aAAa,KAAK,IAAI,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,GAAG;oBACxL,MAAM,GAAG,MAAM,CAAC,WAAW,aAAa,KAAK,MAAM,CAAC;oBACpD,SAAS,IAAI,GAAG;oBAChB,UAAU;wBACR,SAAS,cAAc;4BACrB,gBAAgB;wBAClB,GAAG,QAAQ,QAAQ,QAAQ,KAAK,KAAK,CAAC,eAAe,IAAI,OAAO,MAAM,QAAQ,iBAAiB,KAAK,KAAK,aAAa,MAAM,GAAG;4BAC7H,QAAQ,IAAI,OAAO,CAAC,MAAM;wBAC5B,IAAI,CAAC;oBACP;oBACA,IAAI,QAAQ,QAAQ,QAAQ,KAAK,KAAK,IAAI,IAAI,EAAE;wBAC9C,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,IAAI;wBACtC,QAAQ,MAAM,GAAG;oBACnB;oBACA,SAAS,IAAI,GAAG;oBAChB,OAAO,MAAM,KAAK;gBACpB,KAAK;oBACH,MAAM,SAAS,IAAI;oBACnB,SAAS,IAAI,GAAG;oBAChB,OAAO,IAAI,IAAI;gBACjB,KAAK;oBACH,OAAO,SAAS,IAAI;oBACpB,IAAI,IAAI,EAAE,EAAE;wBACV,SAAS,IAAI,GAAG;wBAChB;oBACF;oBACA,MAAM;gBACR,KAAK;oBACH,OAAO,SAAS,MAAM,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO;gBACzE,KAAK;oBACH,SAAS,IAAI,GAAG;oBAChB,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC;oBAChC,OAAO,KAAK,CAAC,sBAAsB;wBACjC,OAAO,SAAS,EAAE;wBAClB,KAAK;oBACP;oBACA,OAAO,SAAS,MAAM,CAAC,UAAU;gBACnC,KAAK;gBACL,KAAK;oBACH,OAAO,SAAS,IAAI;YACxB;QACF,GAAG,SAAS,MAAM;YAAC;gBAAC;gBAAG;aAAG;SAAC;IAC7B;IACA,OAAO,WAAW,KAAK,CAAC,IAAI,EAAE;AAChC;AACA,SAAS,WAAW,UAAU;IAC5B,IAAI,OAAO,WAAW,aAAa;QACjC,OAAO,GAAG,MAAM,CAAC,WAAW,aAAa,EAAE,MAAM,CAAC,WAAW,cAAc;IAC7E;IACA,OAAO,WAAW,QAAQ;AAC5B;AACA,SAAS;IACP,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;AACjC;AACA,SAAS;IACP,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAC/E,OAAO;QACL,SAAS,SAAS,QAAQ,SAAS;YACjC,IAAI,UAAU,SAAS,QAAQ,KAAK;gBAClC,IAAI;gBACJ,IAAI,MAAM,GAAG,KAAK,MAAM;gBACxB,IAAI,UAAU,KAAK,KAAK,CAAC,CAAC,kBAAkB,MAAM,QAAQ,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB;gBACvH,IAAI,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,MAAM,aAAa,CAAC,CAAC,YAAY,QAAQ,YAAY,KAAK,KAAK,QAAQ,IAAI,GAAG;gBAClJ,UAAU;YACZ;YACA,OAAO,gBAAgB,CAAC,WAAW;YACnC,OAAO;gBACL,OAAO,OAAO,mBAAmB,CAAC,WAAW;YAC/C;QACF;QACA,MAAM,SAAS,KAAK,OAAO;YACzB,IAAI,OAAO,WAAW,aAAa;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,cAAc,cAAc,CAAC,GAAG,UAAU,CAAC,GAAG;oBACtF,WAAW;gBACb;YACF,EAAE,OAAO,SAAS,CAAC;QACrB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next-auth/react/types.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});"], "names": [], "mappings": "AAAA;AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1440, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/next-auth/react/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  SessionContext: true,\n  useSession: true,\n  getSession: true,\n  getCsrfToken: true,\n  getProviders: true,\n  signIn: true,\n  signOut: true,\n  SessionProvider: true\n};\nexports.SessionContext = void 0;\nexports.SessionProvider = SessionProvider;\nexports.getCsrfToken = getCsrfToken;\nexports.getProviders = getProviders;\nexports.getSession = getSession;\nexports.signIn = signIn;\nexports.signOut = signOut;\nexports.useSession = useSession;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _logger2 = _interopRequireWildcard(require(\"../utils/logger\"));\nvar _parseUrl = _interopRequireDefault(require(\"../utils/parse-url\"));\nvar _utils = require(\"../client/_utils\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _types = require(\"./types\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function get() {\n      return _types[key];\n    }\n  });\n});\nvar _process$env$NEXTAUTH, _ref, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _React$createContext;\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar __NEXTAUTH = {\n  baseUrl: (0, _parseUrl.default)((_process$env$NEXTAUTH = process.env.NEXTAUTH_URL) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.VERCEL_URL).origin,\n  basePath: (0, _parseUrl.default)(process.env.NEXTAUTH_URL).path,\n  baseUrlServer: (0, _parseUrl.default)((_ref = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n  basePathServer: (0, _parseUrl.default)((_process$env$NEXTAUTH3 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.NEXTAUTH_URL).path,\n  _lastSync: 0,\n  _session: undefined,\n  _getSession: function _getSession() {}\n};\nvar broadcast = (0, _utils.BroadcastChannel)();\nvar logger = (0, _logger2.proxyLogger)(_logger2.default, __NEXTAUTH.basePath);\nfunction useOnline() {\n  var _React$useState = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false),\n    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n    isOnline = _React$useState2[0],\n    setIsOnline = _React$useState2[1];\n  var setOnline = function setOnline() {\n    return setIsOnline(true);\n  };\n  var setOffline = function setOffline() {\n    return setIsOnline(false);\n  };\n  React.useEffect(function () {\n    window.addEventListener(\"online\", setOnline);\n    window.addEventListener(\"offline\", setOffline);\n    return function () {\n      window.removeEventListener(\"online\", setOnline);\n      window.removeEventListener(\"offline\", setOffline);\n    };\n  }, []);\n  return isOnline;\n}\nvar SessionContext = exports.SessionContext = (_React$createContext = React.createContext) === null || _React$createContext === void 0 ? void 0 : _React$createContext.call(React, undefined);\nfunction useSession(options) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var value = React.useContext(SessionContext);\n  if (!value && process.env.NODE_ENV !== \"production\") {\n    throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n  }\n  var _ref2 = options !== null && options !== void 0 ? options : {},\n    required = _ref2.required,\n    onUnauthenticated = _ref2.onUnauthenticated;\n  var requiredAndNotLoading = required && value.status === \"unauthenticated\";\n  React.useEffect(function () {\n    if (requiredAndNotLoading) {\n      var url = \"/api/auth/signin?\".concat(new URLSearchParams({\n        error: \"SessionRequired\",\n        callbackUrl: window.location.href\n      }));\n      if (onUnauthenticated) onUnauthenticated();else window.location.href = url;\n    }\n  }, [requiredAndNotLoading, onUnauthenticated]);\n  if (requiredAndNotLoading) {\n    return {\n      data: value.data,\n      update: value.update,\n      status: \"loading\"\n    };\n  }\n  return value;\n}\nfunction getSession(_x) {\n  return _getSession2.apply(this, arguments);\n}\nfunction _getSession2() {\n  _getSession2 = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee3(params) {\n    var _params$broadcast;\n    var session;\n    return _regenerator.default.wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          _context3.next = 2;\n          return (0, _utils.fetchData)(\"session\", __NEXTAUTH, logger, params);\n        case 2:\n          session = _context3.sent;\n          if ((_params$broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params$broadcast !== void 0 ? _params$broadcast : true) {\n            broadcast.post({\n              event: \"session\",\n              data: {\n                trigger: \"getSession\"\n              }\n            });\n          }\n          return _context3.abrupt(\"return\", session);\n        case 5:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  }));\n  return _getSession2.apply(this, arguments);\n}\nfunction getCsrfToken(_x2) {\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction _getCsrfToken() {\n  _getCsrfToken = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee4(params) {\n    var response;\n    return _regenerator.default.wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _context4.next = 2;\n          return (0, _utils.fetchData)(\"csrf\", __NEXTAUTH, logger, params);\n        case 2:\n          response = _context4.sent;\n          return _context4.abrupt(\"return\", response === null || response === void 0 ? void 0 : response.csrfToken);\n        case 4:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction getProviders() {\n  return _getProviders.apply(this, arguments);\n}\nfunction _getProviders() {\n  _getProviders = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee5() {\n    return _regenerator.default.wrap(function _callee5$(_context5) {\n      while (1) switch (_context5.prev = _context5.next) {\n        case 0:\n          _context5.next = 2;\n          return (0, _utils.fetchData)(\"providers\", __NEXTAUTH, logger);\n        case 2:\n          return _context5.abrupt(\"return\", _context5.sent);\n        case 3:\n        case \"end\":\n          return _context5.stop();\n      }\n    }, _callee5);\n  }));\n  return _getProviders.apply(this, arguments);\n}\nfunction signIn(_x3, _x4, _x5) {\n  return _signIn.apply(this, arguments);\n}\nfunction _signIn() {\n  _signIn = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee6(provider, options, authorizationParams) {\n    var _ref5, _ref5$callbackUrl, callbackUrl, _ref5$redirect, redirect, baseUrl, providers, isCredentials, isEmail, isSupportingReturn, signInUrl, _signInUrl, res, data, _data$url, url, error;\n    return _regenerator.default.wrap(function _callee6$(_context6) {\n      while (1) switch (_context6.prev = _context6.next) {\n        case 0:\n          _ref5 = options !== null && options !== void 0 ? options : {}, _ref5$callbackUrl = _ref5.callbackUrl, callbackUrl = _ref5$callbackUrl === void 0 ? window.location.href : _ref5$callbackUrl, _ref5$redirect = _ref5.redirect, redirect = _ref5$redirect === void 0 ? true : _ref5$redirect;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context6.next = 4;\n          return getProviders();\n        case 4:\n          providers = _context6.sent;\n          if (providers) {\n            _context6.next = 8;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/error\");\n          return _context6.abrupt(\"return\");\n        case 8:\n          if (!(!provider || !(provider in providers))) {\n            _context6.next = 11;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n            callbackUrl: callbackUrl\n          }));\n          return _context6.abrupt(\"return\");\n        case 11:\n          isCredentials = providers[provider].type === \"credentials\";\n          isEmail = providers[provider].type === \"email\";\n          isSupportingReturn = isCredentials || isEmail;\n          signInUrl = \"\".concat(baseUrl, \"/\").concat(isCredentials ? \"callback\" : \"signin\", \"/\").concat(provider);\n          _signInUrl = \"\".concat(signInUrl).concat(authorizationParams ? \"?\".concat(new URLSearchParams(authorizationParams)) : \"\");\n          _context6.t0 = fetch;\n          _context6.t1 = _signInUrl;\n          _context6.t2 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context6.t3 = URLSearchParams;\n          _context6.t4 = _objectSpread;\n          _context6.t5 = _objectSpread({}, options);\n          _context6.t6 = {};\n          _context6.next = 25;\n          return getCsrfToken();\n        case 25:\n          _context6.t7 = _context6.sent;\n          _context6.t8 = callbackUrl;\n          _context6.t9 = {\n            csrfToken: _context6.t7,\n            callbackUrl: _context6.t8,\n            json: true\n          };\n          _context6.t10 = (0, _context6.t4)(_context6.t5, _context6.t6, _context6.t9);\n          _context6.t11 = new _context6.t3(_context6.t10);\n          _context6.t12 = {\n            method: \"post\",\n            headers: _context6.t2,\n            body: _context6.t11\n          };\n          _context6.next = 33;\n          return (0, _context6.t0)(_context6.t1, _context6.t12);\n        case 33:\n          res = _context6.sent;\n          _context6.next = 36;\n          return res.json();\n        case 36:\n          data = _context6.sent;\n          if (!(redirect || !isSupportingReturn)) {\n            _context6.next = 42;\n            break;\n          }\n          url = (_data$url = data.url) !== null && _data$url !== void 0 ? _data$url : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context6.abrupt(\"return\");\n        case 42:\n          error = new URL(data.url).searchParams.get(\"error\");\n          if (!res.ok) {\n            _context6.next = 46;\n            break;\n          }\n          _context6.next = 46;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 46:\n          return _context6.abrupt(\"return\", {\n            error: error,\n            status: res.status,\n            ok: res.ok,\n            url: error ? null : data.url\n          });\n        case 47:\n        case \"end\":\n          return _context6.stop();\n      }\n    }, _callee6);\n  }));\n  return _signIn.apply(this, arguments);\n}\nfunction signOut(_x6) {\n  return _signOut.apply(this, arguments);\n}\nfunction _signOut() {\n  _signOut = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee7(options) {\n    var _options$redirect;\n    var _ref6, _ref6$callbackUrl, callbackUrl, baseUrl, fetchOptions, res, data, _data$url2, url;\n    return _regenerator.default.wrap(function _callee7$(_context7) {\n      while (1) switch (_context7.prev = _context7.next) {\n        case 0:\n          _ref6 = options !== null && options !== void 0 ? options : {}, _ref6$callbackUrl = _ref6.callbackUrl, callbackUrl = _ref6$callbackUrl === void 0 ? window.location.href : _ref6$callbackUrl;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context7.t0 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context7.t1 = URLSearchParams;\n          _context7.next = 6;\n          return getCsrfToken();\n        case 6:\n          _context7.t2 = _context7.sent;\n          _context7.t3 = callbackUrl;\n          _context7.t4 = {\n            csrfToken: _context7.t2,\n            callbackUrl: _context7.t3,\n            json: true\n          };\n          _context7.t5 = new _context7.t1(_context7.t4);\n          fetchOptions = {\n            method: \"post\",\n            headers: _context7.t0,\n            body: _context7.t5\n          };\n          _context7.next = 13;\n          return fetch(\"\".concat(baseUrl, \"/signout\"), fetchOptions);\n        case 13:\n          res = _context7.sent;\n          _context7.next = 16;\n          return res.json();\n        case 16:\n          data = _context7.sent;\n          broadcast.post({\n            event: \"session\",\n            data: {\n              trigger: \"signout\"\n            }\n          });\n          if (!((_options$redirect = options === null || options === void 0 ? void 0 : options.redirect) !== null && _options$redirect !== void 0 ? _options$redirect : true)) {\n            _context7.next = 23;\n            break;\n          }\n          url = (_data$url2 = data.url) !== null && _data$url2 !== void 0 ? _data$url2 : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context7.abrupt(\"return\");\n        case 23:\n          _context7.next = 25;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 25:\n          return _context7.abrupt(\"return\", data);\n        case 26:\n        case \"end\":\n          return _context7.stop();\n      }\n    }, _callee7);\n  }));\n  return _signOut.apply(this, arguments);\n}\nfunction SessionProvider(props) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var children = props.children,\n    basePath = props.basePath,\n    refetchInterval = props.refetchInterval,\n    refetchWhenOffline = props.refetchWhenOffline;\n  if (basePath) __NEXTAUTH.basePath = basePath;\n  var hasInitialSession = props.session !== undefined;\n  __NEXTAUTH._lastSync = hasInitialSession ? (0, _utils.now)() : 0;\n  var _React$useState3 = React.useState(function () {\n      if (hasInitialSession) __NEXTAUTH._session = props.session;\n      return props.session;\n    }),\n    _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n    session = _React$useState4[0],\n    setSession = _React$useState4[1];\n  var _React$useState5 = React.useState(!hasInitialSession),\n    _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),\n    loading = _React$useState6[0],\n    setLoading = _React$useState6[1];\n  React.useEffect(function () {\n    __NEXTAUTH._getSession = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var _ref4,\n        event,\n        storageEvent,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _ref4 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, event = _ref4.event;\n            _context.prev = 1;\n            storageEvent = event === \"storage\";\n            if (!(storageEvent || __NEXTAUTH._session === undefined)) {\n              _context.next = 10;\n              break;\n            }\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 7;\n            return getSession({\n              broadcast: !storageEvent\n            });\n          case 7:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            return _context.abrupt(\"return\");\n          case 10:\n            if (!(!event || __NEXTAUTH._session === null || (0, _utils.now)() < __NEXTAUTH._lastSync)) {\n              _context.next = 12;\n              break;\n            }\n            return _context.abrupt(\"return\");\n          case 12:\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 15;\n            return getSession();\n          case 15:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            _context.next = 22;\n            break;\n          case 19:\n            _context.prev = 19;\n            _context.t0 = _context[\"catch\"](1);\n            logger.error(\"CLIENT_SESSION_ERROR\", _context.t0);\n          case 22:\n            _context.prev = 22;\n            setLoading(false);\n            return _context.finish(22);\n          case 25:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[1, 19, 22, 25]]);\n    }));\n    __NEXTAUTH._getSession();\n    return function () {\n      __NEXTAUTH._lastSync = 0;\n      __NEXTAUTH._session = undefined;\n      __NEXTAUTH._getSession = function () {};\n    };\n  }, []);\n  React.useEffect(function () {\n    var unsubscribe = broadcast.receive(function () {\n      return __NEXTAUTH._getSession({\n        event: \"storage\"\n      });\n    });\n    return function () {\n      return unsubscribe();\n    };\n  }, []);\n  React.useEffect(function () {\n    var _props$refetchOnWindo = props.refetchOnWindowFocus,\n      refetchOnWindowFocus = _props$refetchOnWindo === void 0 ? true : _props$refetchOnWindo;\n    var visibilityHandler = function visibilityHandler() {\n      if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n        event: \"visibilitychange\"\n      });\n    };\n    document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n    return function () {\n      return document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    };\n  }, [props.refetchOnWindowFocus]);\n  var isOnline = useOnline();\n  var shouldRefetch = refetchWhenOffline !== false || isOnline;\n  React.useEffect(function () {\n    if (refetchInterval && shouldRefetch) {\n      var refetchIntervalTimer = setInterval(function () {\n        if (__NEXTAUTH._session) {\n          __NEXTAUTH._getSession({\n            event: \"poll\"\n          });\n        }\n      }, refetchInterval * 1000);\n      return function () {\n        return clearInterval(refetchIntervalTimer);\n      };\n    }\n  }, [refetchInterval, shouldRefetch]);\n  var value = React.useMemo(function () {\n    return {\n      data: session,\n      status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n      update: function update(data) {\n        return (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n          var newSession;\n          return _regenerator.default.wrap(function _callee2$(_context2) {\n            while (1) switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!(loading || !session)) {\n                  _context2.next = 2;\n                  break;\n                }\n                return _context2.abrupt(\"return\");\n              case 2:\n                setLoading(true);\n                _context2.t0 = _utils.fetchData;\n                _context2.t1 = __NEXTAUTH;\n                _context2.t2 = logger;\n                _context2.next = 8;\n                return getCsrfToken();\n              case 8:\n                _context2.t3 = _context2.sent;\n                _context2.t4 = data;\n                _context2.t5 = {\n                  csrfToken: _context2.t3,\n                  data: _context2.t4\n                };\n                _context2.t6 = {\n                  body: _context2.t5\n                };\n                _context2.t7 = {\n                  req: _context2.t6\n                };\n                _context2.next = 15;\n                return (0, _context2.t0)(\"session\", _context2.t1, _context2.t2, _context2.t7);\n              case 15:\n                newSession = _context2.sent;\n                setLoading(false);\n                if (newSession) {\n                  setSession(newSession);\n                  broadcast.post({\n                    event: \"session\",\n                    data: {\n                      trigger: \"getSession\"\n                    }\n                  });\n                }\n                return _context2.abrupt(\"return\", newSession);\n              case 19:\n              case \"end\":\n                return _context2.stop();\n            }\n          }, _callee2);\n        }))();\n      }\n    };\n  }, [session, loading]);\n  return (0, _jsxRuntime.jsx)(SessionContext.Provider, {\n    value: value,\n    children: children\n  });\n}"], "names": [], "mappings": "AAoD2D;AApD3D;AAEA,IAAI;AACJ,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,IAAI,eAAe;IACjB,gBAAgB;IAChB,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,cAAc;IACd,QAAQ;IACR,SAAS;IACT,iBAAiB;AACnB;AACA,QAAQ,cAAc,GAAG,KAAK;AAC9B,QAAQ,eAAe,GAAG;AAC1B,QAAQ,YAAY,GAAG;AACvB,QAAQ,YAAY,GAAG;AACvB,QAAQ,UAAU,GAAG;AACrB,QAAQ,MAAM,GAAG;AACjB,QAAQ,OAAO,GAAG;AAClB,QAAQ,UAAU,GAAG;AACrB,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;IACvC,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,MAAM;IAC7D,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;IACpD,OAAO,cAAc,CAAC,SAAS,KAAK;QAClC,YAAY;QACZ,KAAK,SAAS;YACZ,OAAO,MAAM,CAAC,IAAI;QACpB;IACF;AACF;AACA,IAAI,uBAAuB,MAAM,wBAAwB,wBAAwB;AACjF,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAS,yBAAyB,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AACnO,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,QAAQ,MAAM,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AACpkB,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACpc,IAAI,aAAa;IACf,SAAS,CAAC,GAAG,UAAU,OAAO,EAAE,CAAC,wBAAwB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM;IACxL,UAAU,CAAC,GAAG,UAAU,OAAO,EAAE,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI;IAC/D,eAAe,CAAC,GAAG,UAAU,OAAO,EAAE,CAAC,OAAO,CAAC,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,MAAM,QAAQ,SAAS,KAAK,IAAI,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM;IACjR,gBAAgB,CAAC,GAAG,UAAU,OAAO,EAAE,CAAC,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI;IAC3M,WAAW;IACX,UAAU;IACV,aAAa,SAAS,eAAe;AACvC;AACA,IAAI,YAAY,CAAC,GAAG,OAAO,gBAAgB;AAC3C,IAAI,SAAS,CAAC,GAAG,SAAS,WAAW,EAAE,SAAS,OAAO,EAAE,WAAW,QAAQ;AAC5E,SAAS;IACP,IAAI,kBAAkB,MAAM,QAAQ,CAAC,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG,QACzF,mBAAmB,CAAC,GAAG,gBAAgB,OAAO,EAAE,iBAAiB,IACjE,WAAW,gBAAgB,CAAC,EAAE,EAC9B,cAAc,gBAAgB,CAAC,EAAE;IACnC,IAAI,YAAY,SAAS;QACvB,OAAO,YAAY;IACrB;IACA,IAAI,aAAa,SAAS;QACxB,OAAO,YAAY;IACrB;IACA,MAAM,SAAS;+BAAC;YACd,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YACnC;uCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;8BAAG,EAAE;IACL,OAAO;AACT;AACA,IAAI,iBAAiB,QAAQ,cAAc,GAAG,CAAC,uBAAuB,MAAM,aAAa,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,IAAI,CAAC,OAAO;AACnL,SAAS,WAAW,OAAO;IACzB,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,QAAQ,MAAM,UAAU,CAAC;IAC7B,IAAI,CAAC,SAAS,oDAAyB,cAAc;QACnD,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,CAAC,GAC9D,WAAW,MAAM,QAAQ,EACzB,oBAAoB,MAAM,iBAAiB;IAC7C,IAAI,wBAAwB,YAAY,MAAM,MAAM,KAAK;IACzD,MAAM,SAAS;gCAAC;YACd,IAAI,uBAAuB;gBACzB,IAAI,MAAM,oBAAoB,MAAM,CAAC,IAAI,gBAAgB;oBACvD,OAAO;oBACP,aAAa,OAAO,QAAQ,CAAC,IAAI;gBACnC;gBACA,IAAI,mBAAmB;qBAAyB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzE;QACF;+BAAG;QAAC;QAAuB;KAAkB;IAC7C,IAAI,uBAAuB;QACzB,OAAO;YACL,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,MAAM;YACpB,QAAQ;QACV;IACF;IACA,OAAO;AACT;AACA,SAAS,WAAW,EAAE;IACpB,OAAO,aAAa,KAAK,CAAC,IAAI,EAAE;AAClC;AACA,SAAS;IACP,eAAe,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,MAAM;QAC/F,IAAI;QACJ,IAAI;QACJ,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,WAAW,YAAY,QAAQ;gBAC9D,KAAK;oBACH,UAAU,UAAU,IAAI;oBACxB,IAAI,CAAC,oBAAoB,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,SAAS,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,MAAM;wBAC9J,UAAU,IAAI,CAAC;4BACb,OAAO;4BACP,MAAM;gCACJ,SAAS;4BACX;wBACF;oBACF;oBACA,OAAO,UAAU,MAAM,CAAC,UAAU;gBACpC,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,aAAa,KAAK,CAAC,IAAI,EAAE;AAClC;AACA,SAAS,aAAa,GAAG;IACvB,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS;IACP,gBAAgB,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,MAAM;QAChG,IAAI;QACJ,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,QAAQ,YAAY,QAAQ;gBAC3D,KAAK;oBACH,WAAW,UAAU,IAAI;oBACzB,OAAO,UAAU,MAAM,CAAC,UAAU,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,SAAS;gBAC1G,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS;IACP,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS;IACP,gBAAgB,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;QACjF,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,aAAa,YAAY;gBACxD,KAAK;oBACH,OAAO,UAAU,MAAM,CAAC,UAAU,UAAU,IAAI;gBAClD,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG;IAC3B,OAAO,QAAQ,KAAK,CAAC,IAAI,EAAE;AAC7B;AACA,SAAS;IACP,UAAU,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ,EAAE,OAAO,EAAE,mBAAmB;QAC1H,IAAI,OAAO,mBAAmB,aAAa,gBAAgB,UAAU,SAAS,WAAW,eAAe,SAAS,oBAAoB,WAAW,YAAY,KAAK,MAAM,WAAW,KAAK;QACvL,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,CAAC,GAAG,oBAAoB,MAAM,WAAW,EAAE,cAAc,sBAAsB,KAAK,IAAI,OAAO,QAAQ,CAAC,IAAI,GAAG,mBAAmB,iBAAiB,MAAM,QAAQ,EAAE,WAAW,mBAAmB,KAAK,IAAI,OAAO;oBAC5Q,UAAU,CAAC,GAAG,OAAO,UAAU,EAAE;oBACjC,UAAU,IAAI,GAAG;oBACjB,OAAO;gBACT,KAAK;oBACH,YAAY,UAAU,IAAI;oBAC1B,IAAI,WAAW;wBACb,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,OAAO,QAAQ,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS;oBAC1C,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,YAAY,SAAS,CAAC,GAAG;wBAC5C,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,OAAO,QAAQ,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,YAAY,MAAM,CAAC,IAAI,gBAAgB;wBAC/E,aAAa;oBACf;oBACA,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,gBAAgB,SAAS,CAAC,SAAS,CAAC,IAAI,KAAK;oBAC7C,UAAU,SAAS,CAAC,SAAS,CAAC,IAAI,KAAK;oBACvC,qBAAqB,iBAAiB;oBACtC,YAAY,GAAG,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,gBAAgB,aAAa,UAAU,KAAK,MAAM,CAAC;oBAC9F,aAAa,GAAG,MAAM,CAAC,WAAW,MAAM,CAAC,sBAAsB,IAAI,MAAM,CAAC,IAAI,gBAAgB,wBAAwB;oBACtH,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;wBACb,gBAAgB;oBAClB;oBACA,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG,cAAc,CAAC,GAAG;oBACjC,UAAU,EAAE,GAAG,CAAC;oBAChB,UAAU,IAAI,GAAG;oBACjB,OAAO;gBACT,KAAK;oBACH,UAAU,EAAE,GAAG,UAAU,IAAI;oBAC7B,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;wBACb,WAAW,UAAU,EAAE;wBACvB,aAAa,UAAU,EAAE;wBACzB,MAAM;oBACR;oBACA,UAAU,GAAG,GAAG,CAAC,GAAG,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE;oBAC1E,UAAU,GAAG,GAAG,IAAI,UAAU,EAAE,CAAC,UAAU,GAAG;oBAC9C,UAAU,GAAG,GAAG;wBACd,QAAQ;wBACR,SAAS,UAAU,EAAE;wBACrB,MAAM,UAAU,GAAG;oBACrB;oBACA,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,GAAG;gBACtD,KAAK;oBACH,MAAM,UAAU,IAAI;oBACpB,UAAU,IAAI,GAAG;oBACjB,OAAO,IAAI,IAAI;gBACjB,KAAK;oBACH,OAAO,UAAU,IAAI;oBACrB,IAAI,CAAC,CAAC,YAAY,CAAC,kBAAkB,GAAG;wBACtC,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,MAAM,CAAC,YAAY,KAAK,GAAG,MAAM,QAAQ,cAAc,KAAK,IAAI,YAAY;oBAC5E,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACvB,IAAI,IAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM;oBAC7C,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,QAAQ,IAAI,IAAI,KAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC;oBAC3C,IAAI,CAAC,IAAI,EAAE,EAAE;wBACX,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,UAAU,IAAI,GAAG;oBACjB,OAAO,WAAW,WAAW,CAAC;wBAC5B,OAAO;oBACT;gBACF,KAAK;oBACH,OAAO,UAAU,MAAM,CAAC,UAAU;wBAChC,OAAO;wBACP,QAAQ,IAAI,MAAM;wBAClB,IAAI,IAAI,EAAE;wBACV,KAAK,QAAQ,OAAO,KAAK,GAAG;oBAC9B;gBACF,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,QAAQ,KAAK,CAAC,IAAI,EAAE;AAC7B;AACA,SAAS,QAAQ,GAAG;IAClB,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AACA,SAAS;IACP,WAAW,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,OAAO;QAC5F,IAAI;QACJ,IAAI,OAAO,mBAAmB,aAAa,SAAS,cAAc,KAAK,MAAM,YAAY;QACzF,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,CAAC,GAAG,oBAAoB,MAAM,WAAW,EAAE,cAAc,sBAAsB,KAAK,IAAI,OAAO,QAAQ,CAAC,IAAI,GAAG;oBAC1K,UAAU,CAAC,GAAG,OAAO,UAAU,EAAE;oBACjC,UAAU,EAAE,GAAG;wBACb,gBAAgB;oBAClB;oBACA,UAAU,EAAE,GAAG;oBACf,UAAU,IAAI,GAAG;oBACjB,OAAO;gBACT,KAAK;oBACH,UAAU,EAAE,GAAG,UAAU,IAAI;oBAC7B,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;wBACb,WAAW,UAAU,EAAE;wBACvB,aAAa,UAAU,EAAE;wBACzB,MAAM;oBACR;oBACA,UAAU,EAAE,GAAG,IAAI,UAAU,EAAE,CAAC,UAAU,EAAE;oBAC5C,eAAe;wBACb,QAAQ;wBACR,SAAS,UAAU,EAAE;wBACrB,MAAM,UAAU,EAAE;oBACpB;oBACA,UAAU,IAAI,GAAG;oBACjB,OAAO,MAAM,GAAG,MAAM,CAAC,SAAS,aAAa;gBAC/C,KAAK;oBACH,MAAM,UAAU,IAAI;oBACpB,UAAU,IAAI,GAAG;oBACjB,OAAO,IAAI,IAAI;gBACjB,KAAK;oBACH,OAAO,UAAU,IAAI;oBACrB,UAAU,IAAI,CAAC;wBACb,OAAO;wBACP,MAAM;4BACJ,SAAS;wBACX;oBACF;oBACA,IAAI,CAAC,CAAC,CAAC,oBAAoB,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,IAAI,GAAG;wBACnK,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,MAAM,CAAC,aAAa,KAAK,GAAG,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa;oBAC/E,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACvB,IAAI,IAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM;oBAC7C,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,WAAW,WAAW,CAAC;wBAC5B,OAAO;oBACT;gBACF,KAAK;oBACH,OAAO,UAAU,MAAM,CAAC,UAAU;gBACpC,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AACA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,WAAW,MAAM,QAAQ,EAC3B,WAAW,MAAM,QAAQ,EACzB,kBAAkB,MAAM,eAAe,EACvC,qBAAqB,MAAM,kBAAkB;IAC/C,IAAI,UAAU,WAAW,QAAQ,GAAG;IACpC,IAAI,oBAAoB,MAAM,OAAO,KAAK;IAC1C,WAAW,SAAS,GAAG,oBAAoB,CAAC,GAAG,OAAO,GAAG,MAAM;IAC/D,IAAI,mBAAmB,MAAM,QAAQ;sDAAC;YAClC,IAAI,mBAAmB,WAAW,QAAQ,GAAG,MAAM,OAAO;YAC1D,OAAO,MAAM,OAAO;QACtB;sDACA,mBAAmB,CAAC,GAAG,gBAAgB,OAAO,EAAE,kBAAkB,IAClE,UAAU,gBAAgB,CAAC,EAAE,EAC7B,aAAa,gBAAgB,CAAC,EAAE;IAClC,IAAI,mBAAmB,MAAM,QAAQ,CAAC,CAAC,oBACrC,mBAAmB,CAAC,GAAG,gBAAgB,OAAO,EAAE,kBAAkB,IAClE,UAAU,gBAAgB,CAAC,EAAE,EAC7B,aAAa,gBAAgB,CAAC,EAAE;IAClC,MAAM,SAAS;qCAAC;YACd,WAAW,WAAW,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;gBAC1F,IAAI,OACF,OACA,cACA,QAAQ;gBACV,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;oBACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;wBAC7C,KAAK;4BACH,QAAQ,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,QAAQ,MAAM,KAAK;4BACvF,SAAS,IAAI,GAAG;4BAChB,eAAe,UAAU;4BACzB,IAAI,CAAC,CAAC,gBAAgB,WAAW,QAAQ,KAAK,SAAS,GAAG;gCACxD,SAAS,IAAI,GAAG;gCAChB;4BACF;4BACA,WAAW,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG;4BACrC,SAAS,IAAI,GAAG;4BAChB,OAAO,WAAW;gCAChB,WAAW,CAAC;4BACd;wBACF,KAAK;4BACH,WAAW,QAAQ,GAAG,SAAS,IAAI;4BACnC,WAAW,WAAW,QAAQ;4BAC9B,OAAO,SAAS,MAAM,CAAC;wBACzB,KAAK;4BACH,IAAI,CAAC,CAAC,CAAC,SAAS,WAAW,QAAQ,KAAK,QAAQ,CAAC,GAAG,OAAO,GAAG,MAAM,WAAW,SAAS,GAAG;gCACzF,SAAS,IAAI,GAAG;gCAChB;4BACF;4BACA,OAAO,SAAS,MAAM,CAAC;wBACzB,KAAK;4BACH,WAAW,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG;4BACrC,SAAS,IAAI,GAAG;4BAChB,OAAO;wBACT,KAAK;4BACH,WAAW,QAAQ,GAAG,SAAS,IAAI;4BACnC,WAAW,WAAW,QAAQ;4BAC9B,SAAS,IAAI,GAAG;4BAChB;wBACF,KAAK;4BACH,SAAS,IAAI,GAAG;4BAChB,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC;4BAChC,OAAO,KAAK,CAAC,wBAAwB,SAAS,EAAE;wBAClD,KAAK;4BACH,SAAS,IAAI,GAAG;4BAChB,WAAW;4BACX,OAAO,SAAS,MAAM,CAAC;wBACzB,KAAK;wBACL,KAAK;4BACH,OAAO,SAAS,IAAI;oBACxB;gBACF,GAAG,SAAS,MAAM;oBAAC;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;iBAAC;YACrC;YACA,WAAW,WAAW;YACtB;6CAAO;oBACL,WAAW,SAAS,GAAG;oBACvB,WAAW,QAAQ,GAAG;oBACtB,WAAW,WAAW;qDAAG,YAAa;;gBACxC;;QACF;oCAAG,EAAE;IACL,MAAM,SAAS;qCAAC;YACd,IAAI,cAAc,UAAU,OAAO;yDAAC;oBAClC,OAAO,WAAW,WAAW,CAAC;wBAC5B,OAAO;oBACT;gBACF;;YACA;6CAAO;oBACL,OAAO;gBACT;;QACF;oCAAG,EAAE;IACL,MAAM,SAAS;qCAAC;YACd,IAAI,wBAAwB,MAAM,oBAAoB,EACpD,uBAAuB,0BAA0B,KAAK,IAAI,OAAO;YACnE,IAAI,oBAAoB,SAAS;gBAC/B,IAAI,wBAAwB,SAAS,eAAe,KAAK,WAAW,WAAW,WAAW,CAAC;oBACzF,OAAO;gBACT;YACF;YACA,SAAS,gBAAgB,CAAC,oBAAoB,mBAAmB;YACjE;6CAAO;oBACL,OAAO,SAAS,mBAAmB,CAAC,oBAAoB,mBAAmB;gBAC7E;;QACF;oCAAG;QAAC,MAAM,oBAAoB;KAAC;IAC/B,IAAI,WAAW;IACf,IAAI,gBAAgB,uBAAuB,SAAS;IACpD,MAAM,SAAS;qCAAC;YACd,IAAI,mBAAmB,eAAe;gBACpC,IAAI,uBAAuB;sEAAY;wBACrC,IAAI,WAAW,QAAQ,EAAE;4BACvB,WAAW,WAAW,CAAC;gCACrB,OAAO;4BACT;wBACF;oBACF;qEAAG,kBAAkB;gBACrB;iDAAO;wBACL,OAAO,cAAc;oBACvB;;YACF;QACF;oCAAG;QAAC;QAAiB;KAAc;IACnC,IAAI,QAAQ,MAAM,OAAO;0CAAC;YACxB,OAAO;gBACL,MAAM;gBACN,QAAQ,UAAU,YAAY,UAAU,kBAAkB;gBAC1D,QAAQ,SAAS,OAAO,IAAI;oBAC1B,OAAO,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;wBACxE,IAAI;wBACJ,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;4BAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gCAC/C,KAAK;oCACH,IAAI,CAAC,CAAC,WAAW,CAAC,OAAO,GAAG;wCAC1B,UAAU,IAAI,GAAG;wCACjB;oCACF;oCACA,OAAO,UAAU,MAAM,CAAC;gCAC1B,KAAK;oCACH,WAAW;oCACX,UAAU,EAAE,GAAG,OAAO,SAAS;oCAC/B,UAAU,EAAE,GAAG;oCACf,UAAU,EAAE,GAAG;oCACf,UAAU,IAAI,GAAG;oCACjB,OAAO;gCACT,KAAK;oCACH,UAAU,EAAE,GAAG,UAAU,IAAI;oCAC7B,UAAU,EAAE,GAAG;oCACf,UAAU,EAAE,GAAG;wCACb,WAAW,UAAU,EAAE;wCACvB,MAAM,UAAU,EAAE;oCACpB;oCACA,UAAU,EAAE,GAAG;wCACb,MAAM,UAAU,EAAE;oCACpB;oCACA,UAAU,EAAE,GAAG;wCACb,KAAK,UAAU,EAAE;oCACnB;oCACA,UAAU,IAAI,GAAG;oCACjB,OAAO,CAAC,GAAG,UAAU,EAAE,EAAE,WAAW,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE;gCAC9E,KAAK;oCACH,aAAa,UAAU,IAAI;oCAC3B,WAAW;oCACX,IAAI,YAAY;wCACd,WAAW;wCACX,UAAU,IAAI,CAAC;4CACb,OAAO;4CACP,MAAM;gDACJ,SAAS;4CACX;wCACF;oCACF;oCACA,OAAO,UAAU,MAAM,CAAC,UAAU;gCACpC,KAAK;gCACL,KAAK;oCACH,OAAO,UAAU,IAAI;4BACzB;wBACF,GAAG;oBACL;gBACF;YACF;QACF;yCAAG;QAAC;QAAS;KAAQ;IACrB,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,eAAe,QAAQ,EAAE;QACnD,OAAO;QACP,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-parser/build/esm/commons.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,uBAAuB;AACjE,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,QAAQ,GAAG;AACxB,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,UAAU,GAAG;AAC1B,YAAY,CAAC,UAAU,GAAG;AAC1B,YAAY,CAAC,OAAO,GAAG;AACvB,MAAM,uBAAuB,OAAO,MAAM,CAAC;AAC3C,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAC;IAC/B,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG;AAC9C;AACA,MAAM,eAAe;IAAE,MAAM;IAAS,MAAM;AAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-parser/build/esm/encodePacket.browser.js"], "sourcesContent": ["import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU;AACjD,MAAM,wBAAwB,OAAO,gBAAgB;AACrD,mDAAmD;AACnD,MAAM,SAAS,CAAC;IACZ,OAAO,OAAO,YAAY,MAAM,KAAK,aAC/B,YAAY,MAAM,CAAC,OACnB,OAAO,IAAI,MAAM,YAAY;AACvC;AACA,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,gBAAgB;IAClD,IAAI,kBAAkB,gBAAgB,MAAM;QACxC,IAAI,gBAAgB;YAChB,OAAO,SAAS;QACpB,OACK;YACD,OAAO,mBAAmB,MAAM;QACpC;IACJ,OACK,IAAI,yBACL,CAAC,gBAAgB,eAAe,OAAO,KAAK,GAAG;QAC/C,IAAI,gBAAgB;YAChB,OAAO,SAAS;QACpB,OACK;YACD,OAAO,mBAAmB,IAAI,KAAK;gBAAC;aAAK,GAAG;QAChD;IACJ;IACA,eAAe;IACf,OAAO,SAAS,oKAAA,CAAA,eAAY,CAAC,KAAK,GAAG,CAAC,QAAQ,EAAE;AACpD;AACA,MAAM,qBAAqB,CAAC,MAAM;IAC9B,MAAM,aAAa,IAAI;IACvB,WAAW,MAAM,GAAG;QAChB,MAAM,UAAU,WAAW,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/C,SAAS,MAAM,CAAC,WAAW,EAAE;IACjC;IACA,OAAO,WAAW,aAAa,CAAC;AACpC;AACA,SAAS,QAAQ,IAAI;IACjB,IAAI,gBAAgB,YAAY;QAC5B,OAAO;IACX,OACK,IAAI,gBAAgB,aAAa;QAClC,OAAO,IAAI,WAAW;IAC1B,OACK;QACD,OAAO,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU;IACvE;AACJ;AACA,IAAI;AACG,SAAS,qBAAqB,MAAM,EAAE,QAAQ;IACjD,IAAI,kBAAkB,OAAO,IAAI,YAAY,MAAM;QAC/C,OAAO,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC;IACxD,OACK,IAAI,yBACL,CAAC,OAAO,IAAI,YAAY,eAAe,OAAO,OAAO,IAAI,CAAC,GAAG;QAC7D,OAAO,SAAS,QAAQ,OAAO,IAAI;IACvC;IACA,aAAa,QAAQ,OAAO,CAAC;QACzB,IAAI,CAAC,cAAc;YACf,eAAe,IAAI;QACvB;QACA,SAAS,aAAa,MAAM,CAAC;IACjC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2161, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js"], "sourcesContent": ["// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;;AAC/D,MAAM,QAAQ;AACd,wCAAwC;AACxC,MAAM,SAAS,OAAO,eAAe,cAAc,EAAE,GAAG,IAAI,WAAW;AACvE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;IACnC,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,GAAG;AAClC;AACO,MAAM,SAAS,CAAC;IACnB,IAAI,QAAQ,IAAI,WAAW,cAAc,GAAG,MAAM,MAAM,MAAM,EAAE,SAAS;IACzE,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QACzB,UAAU,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE;QAC9B,UAAU,KAAK,CAAC,AAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,IAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAG;QAC5D,UAAU,KAAK,CAAC,AAAC,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,IAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAG;QACjE,UAAU,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,GAAG;IACtC;IACA,IAAI,MAAM,MAAM,GAAG;QACf,SAAS,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK;IACtD,OACK,IAAI,MAAM,MAAM,GAAG;QACpB,SAAS,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK;IACtD;IACA,OAAO;AACX;AACO,MAAM,SAAS,CAAC;IACnB,IAAI,eAAe,OAAO,MAAM,GAAG,MAAM,MAAM,OAAO,MAAM,EAAE,GAAG,IAAI,GAAG,UAAU,UAAU,UAAU;IACtG,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,KAAK;QACnC;QACA,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,KAAK;YACnC;QACJ;IACJ;IACA,MAAM,cAAc,IAAI,YAAY,eAAe,QAAQ,IAAI,WAAW;IAC1E,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QACzB,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,GAAG;QACvC,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,IAAI,GAAG;QAC3C,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,IAAI,GAAG;QAC3C,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,IAAI,GAAG;QAC3C,KAAK,CAAC,IAAI,GAAG,AAAC,YAAY,IAAM,YAAY;QAC5C,KAAK,CAAC,IAAI,GAAG,AAAC,CAAC,WAAW,EAAE,KAAK,IAAM,YAAY;QACnD,KAAK,CAAC,IAAI,GAAG,AAAC,CAAC,WAAW,CAAC,KAAK,IAAM,WAAW;IACrD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2213, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-parser/build/esm/decodePacket.browser.js"], "sourcesContent": ["import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,MAAM,wBAAwB,OAAO,gBAAgB;AAC9C,MAAM,eAAe,CAAC,eAAe;IACxC,IAAI,OAAO,kBAAkB,UAAU;QACnC,OAAO;YACH,MAAM;YACN,MAAM,UAAU,eAAe;QACnC;IACJ;IACA,MAAM,OAAO,cAAc,MAAM,CAAC;IAClC,IAAI,SAAS,KAAK;QACd,OAAO;YACH,MAAM;YACN,MAAM,mBAAmB,cAAc,SAAS,CAAC,IAAI;QACzD;IACJ;IACA,MAAM,aAAa,oKAAA,CAAA,uBAAoB,CAAC,KAAK;IAC7C,IAAI,CAAC,YAAY;QACb,OAAO,oKAAA,CAAA,eAAY;IACvB;IACA,OAAO,cAAc,MAAM,GAAG,IACxB;QACE,MAAM,oKAAA,CAAA,uBAAoB,CAAC,KAAK;QAChC,MAAM,cAAc,SAAS,CAAC;IAClC,IACE;QACE,MAAM,oKAAA,CAAA,uBAAoB,CAAC,KAAK;IACpC;AACR;AACA,MAAM,qBAAqB,CAAC,MAAM;IAC9B,IAAI,uBAAuB;QACvB,MAAM,UAAU,CAAA,GAAA,6LAAA,CAAA,SAAM,AAAD,EAAE;QACvB,OAAO,UAAU,SAAS;IAC9B,OACK;QACD,OAAO;YAAE,QAAQ;YAAM;QAAK,GAAG,4BAA4B;IAC/D;AACJ;AACA,MAAM,YAAY,CAAC,MAAM;IACrB,OAAQ;QACJ,KAAK;YACD,IAAI,gBAAgB,MAAM;gBACtB,qCAAqC;gBACrC,OAAO;YACX,OACK;gBACD,yCAAyC;gBACzC,OAAO,IAAI,KAAK;oBAAC;iBAAK;YAC1B;QACJ,KAAK;QACL;YACI,IAAI,gBAAgB,aAAa;gBAC7B,0EAA0E;gBAC1E,OAAO;YACX,OACK;gBACD,iCAAiC;gBACjC,OAAO,KAAK,MAAM;YACtB;IACR;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2286, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-parser/build/esm/index.js"], "sourcesContent": ["import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AACA,MAAM,YAAY,OAAO,YAAY,CAAC,KAAK,mEAAmE;AAC9G,MAAM,gBAAgB,CAAC,SAAS;IAC5B,6FAA6F;IAC7F,MAAM,SAAS,QAAQ,MAAM;IAC7B,MAAM,iBAAiB,IAAI,MAAM;IACjC,IAAI,QAAQ;IACZ,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACrB,2CAA2C;QAC3C,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO,CAAC;YACzB,cAAc,CAAC,EAAE,GAAG;YACpB,IAAI,EAAE,UAAU,QAAQ;gBACpB,SAAS,eAAe,IAAI,CAAC;YACjC;QACJ;IACJ;AACJ;AACA,MAAM,gBAAgB,CAAC,gBAAgB;IACnC,MAAM,iBAAiB,eAAe,KAAK,CAAC;IAC5C,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;QAC5C,MAAM,gBAAgB,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,cAAc,CAAC,EAAE,EAAE;QACtD,QAAQ,IAAI,CAAC;QACb,IAAI,cAAc,IAAI,KAAK,SAAS;YAChC;QACJ;IACJ;IACA,OAAO;AACX;AACO,SAAS;IACZ,OAAO,IAAI,gBAAgB;QACvB,WAAU,MAAM,EAAE,UAAU;YACxB,CAAA,GAAA,oLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,CAAC;gBAC1B,MAAM,gBAAgB,cAAc,MAAM;gBAC1C,IAAI;gBACJ,sJAAsJ;gBACtJ,IAAI,gBAAgB,KAAK;oBACrB,SAAS,IAAI,WAAW;oBACxB,IAAI,SAAS,OAAO,MAAM,EAAE,QAAQ,CAAC,GAAG;gBAC5C,OACK,IAAI,gBAAgB,OAAO;oBAC5B,SAAS,IAAI,WAAW;oBACxB,MAAM,OAAO,IAAI,SAAS,OAAO,MAAM;oBACvC,KAAK,QAAQ,CAAC,GAAG;oBACjB,KAAK,SAAS,CAAC,GAAG;gBACtB,OACK;oBACD,SAAS,IAAI,WAAW;oBACxB,MAAM,OAAO,IAAI,SAAS,OAAO,MAAM;oBACvC,KAAK,QAAQ,CAAC,GAAG;oBACjB,KAAK,YAAY,CAAC,GAAG,OAAO;gBAChC;gBACA,0EAA0E;gBAC1E,IAAI,OAAO,IAAI,IAAI,OAAO,OAAO,IAAI,KAAK,UAAU;oBAChD,MAAM,CAAC,EAAE,IAAI;gBACjB;gBACA,WAAW,OAAO,CAAC;gBACnB,WAAW,OAAO,CAAC;YACvB;QACJ;IACJ;AACJ;AACA,IAAI;AACJ,SAAS,YAAY,MAAM;IACvB,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,MAAM,EAAE;AAC7D;AACA,SAAS,aAAa,MAAM,EAAE,IAAI;IAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,MAAM;QAC3B,OAAO,OAAO,KAAK;IACvB;IACA,MAAM,SAAS,IAAI,WAAW;IAC9B,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC3B,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI;QAC1B,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;YACxB,OAAO,KAAK;YACZ,IAAI;QACR;IACJ;IACA,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;QACvC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;IAChC;IACA,OAAO;AACX;AACO,SAAS,0BAA0B,UAAU,EAAE,UAAU;IAC5D,IAAI,CAAC,cAAc;QACf,eAAe,IAAI;IACvB;IACA,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,EAAE,qBAAqB;IACnC,IAAI,iBAAiB,CAAC;IACtB,IAAI,WAAW;IACf,OAAO,IAAI,gBAAgB;QACvB,WAAU,KAAK,EAAE,UAAU;YACvB,OAAO,IAAI,CAAC;YACZ,MAAO,KAAM;gBACT,IAAI,UAAU,EAAE,qBAAqB,KAAI;oBACrC,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,SAAS,aAAa,QAAQ;oBACpC,WAAW,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM;oBAClC,iBAAiB,MAAM,CAAC,EAAE,GAAG;oBAC7B,IAAI,iBAAiB,KAAK;wBACtB,QAAQ,EAAE,sBAAsB;oBACpC,OACK,IAAI,mBAAmB,KAAK;wBAC7B,QAAQ,EAAE,iCAAiC;oBAC/C,OACK;wBACD,QAAQ,EAAE,iCAAiC;oBAC/C;gBACJ,OACK,IAAI,UAAU,EAAE,iCAAiC,KAAI;oBACtD,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,cAAc,aAAa,QAAQ;oBACzC,iBAAiB,IAAI,SAAS,YAAY,MAAM,EAAE,YAAY,UAAU,EAAE,YAAY,MAAM,EAAE,SAAS,CAAC;oBACxG,QAAQ,EAAE,sBAAsB;gBACpC,OACK,IAAI,UAAU,EAAE,iCAAiC,KAAI;oBACtD,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,cAAc,aAAa,QAAQ;oBACzC,MAAM,OAAO,IAAI,SAAS,YAAY,MAAM,EAAE,YAAY,UAAU,EAAE,YAAY,MAAM;oBACxF,MAAM,IAAI,KAAK,SAAS,CAAC;oBACzB,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,GAAG;wBAC9B,qDAAqD;wBACrD,WAAW,OAAO,CAAC,oKAAA,CAAA,eAAY;wBAC/B;oBACJ;oBACA,iBAAiB,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,SAAS,CAAC;oBACtD,QAAQ,EAAE,sBAAsB;gBACpC,OACK;oBACD,IAAI,YAAY,UAAU,gBAAgB;wBACtC;oBACJ;oBACA,MAAM,OAAO,aAAa,QAAQ;oBAClC,WAAW,OAAO,CAAC,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,WAAW,OAAO,aAAa,MAAM,CAAC,OAAO;oBAC7E,QAAQ,EAAE,qBAAqB;gBACnC;gBACA,IAAI,mBAAmB,KAAK,iBAAiB,YAAY;oBACrD,WAAW,OAAO,CAAC,oKAAA,CAAA,eAAY;oBAC/B;gBACJ;YACJ;QACJ;IACJ;AACJ;AACO,MAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2462, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/%40socket.io/component-emitter/lib/esm/index.js"], "sourcesContent": ["/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAEM,SAAS,QAAQ,GAAG;IACzB,IAAI,KAAK,OAAO,MAAM;AACxB;AAEA;;;;;;CAMC,GAED,SAAS,MAAM,GAAG;IAChB,IAAK,IAAI,OAAO,QAAQ,SAAS,CAAE;QACjC,GAAG,CAAC,IAAI,GAAG,QAAQ,SAAS,CAAC,IAAI;IACnC;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GAED,QAAQ,SAAS,CAAC,EAAE,GACpB,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAS,KAAK,EAAE,EAAE;IACrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IACtC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,IAAI,EAAE,EAC/D,IAAI,CAAC;IACR,OAAO,IAAI;AACb;AAEA;;;;;;;;CAQC,GAED,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,EAAE,EAAE;IACzC,SAAS;QACP,IAAI,CAAC,GAAG,CAAC,OAAO;QAChB,GAAG,KAAK,CAAC,IAAI,EAAE;IACjB;IAEA,GAAG,EAAE,GAAG;IACR,IAAI,CAAC,EAAE,CAAC,OAAO;IACf,OAAO,IAAI;AACb;AAEA;;;;;;;;CAQC,GAED,QAAQ,SAAS,CAAC,GAAG,GACrB,QAAQ,SAAS,CAAC,cAAc,GAChC,QAAQ,SAAS,CAAC,kBAAkB,GACpC,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE,EAAE;IACxD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IAEtC,MAAM;IACN,IAAI,KAAK,UAAU,MAAM,EAAE;QACzB,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,OAAO,IAAI;IACb;IAEA,iBAAiB;IACjB,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IAC5C,IAAI,CAAC,WAAW,OAAO,IAAI;IAE3B,sBAAsB;IACtB,IAAI,KAAK,UAAU,MAAM,EAAE;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;QACnC,OAAO,IAAI;IACb;IAEA,0BAA0B;IAC1B,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,KAAK,SAAS,CAAC,EAAE;QACjB,IAAI,OAAO,MAAM,GAAG,EAAE,KAAK,IAAI;YAC7B,UAAU,MAAM,CAAC,GAAG;YACpB;QACF;IACF;IAEA,uDAAuD;IACvD,8CAA8C;IAC9C,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IACrC;IAEA,OAAO,IAAI;AACb;AAEA;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK;IACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IAEtC,IAAI,OAAO,IAAI,MAAM,UAAU,MAAM,GAAG,IACpC,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IAE5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,CAAC,IAAI,EAAE,GAAG,SAAS,CAAC,EAAE;IAC5B;IAEA,IAAI,WAAW;QACb,YAAY,UAAU,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YACpD,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE;QAC3B;IACF;IAEA,OAAO,IAAI;AACb;AAEA,oDAAoD;AACpD,QAAQ,SAAS,CAAC,YAAY,GAAG,QAAQ,SAAS,CAAC,IAAI;AAEvD;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAS,KAAK;IAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IACtC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,IAAI,EAAE;AAC3C;AAEA;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,KAAK;IAC7C,OAAO,CAAC,CAAE,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2599, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/globals.js"], "sourcesContent": ["export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,WAAW,CAAC;IACrB,MAAM,qBAAqB,OAAO,YAAY,cAAc,OAAO,QAAQ,OAAO,KAAK;IACvF,IAAI,oBAAoB;QACpB,OAAO,CAAC,KAAO,QAAQ,OAAO,GAAG,IAAI,CAAC;IAC1C,OACK;QACD,OAAO,CAAC,IAAI,eAAiB,aAAa,IAAI;IAClD;AACJ,CAAC;AACM,MAAM,iBAAiB,CAAC;IAC3B,IAAI,OAAO,SAAS,aAAa;QAC7B,OAAO;IACX,OACK,IAAI,OAAO,WAAW,aAAa;QACpC,OAAO;IACX,OACK;QACD,OAAO,SAAS;IACpB;AACJ,CAAC;AACM,MAAM,oBAAoB;AAC1B,SAAS,mBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2630, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/util.js"], "sourcesContent": ["import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,SAAS,KAAK,GAAG,EAAE,GAAG,IAAI;IAC7B,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,IAAI,IAAI,cAAc,CAAC,IAAI;YACvB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACnB;QACA,OAAO;IACX,GAAG,CAAC;AACR;AACA,qFAAqF;AACrF,MAAM,qBAAqB,oKAAA,CAAA,iBAAU,CAAC,UAAU;AAChD,MAAM,uBAAuB,oKAAA,CAAA,iBAAU,CAAC,YAAY;AAC7C,SAAS,sBAAsB,GAAG,EAAE,IAAI;IAC3C,IAAI,KAAK,eAAe,EAAE;QACtB,IAAI,YAAY,GAAG,mBAAmB,IAAI,CAAC,oKAAA,CAAA,iBAAU;QACrD,IAAI,cAAc,GAAG,qBAAqB,IAAI,CAAC,oKAAA,CAAA,iBAAU;IAC7D,OACK;QACD,IAAI,YAAY,GAAG,oKAAA,CAAA,iBAAU,CAAC,UAAU,CAAC,IAAI,CAAC,oKAAA,CAAA,iBAAU;QACxD,IAAI,cAAc,GAAG,oKAAA,CAAA,iBAAU,CAAC,YAAY,CAAC,IAAI,CAAC,oKAAA,CAAA,iBAAU;IAChE;AACJ;AACA,qFAAqF;AACrF,MAAM,kBAAkB;AAEjB,SAAS,WAAW,GAAG;IAC1B,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO,WAAW;IACtB;IACA,sBAAsB;IACtB,OAAO,KAAK,IAAI,CAAC,CAAC,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI;AACpD;AACA,SAAS,WAAW,GAAG;IACnB,IAAI,IAAI,GAAG,SAAS;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,IAAK;QACxC,IAAI,IAAI,UAAU,CAAC;QACnB,IAAI,IAAI,MAAM;YACV,UAAU;QACd,OACK,IAAI,IAAI,OAAO;YAChB,UAAU;QACd,OACK,IAAI,IAAI,UAAU,KAAK,QAAQ;YAChC,UAAU;QACd,OACK;YACD;YACA,UAAU;QACd;IACJ;IACA,OAAO;AACX;AAIO,SAAS;IACZ,OAAQ,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KACtC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2693, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/contrib/parseqs.js"], "sourcesContent": ["// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD;;;;;;CAMC;;;;AACM,SAAS,OAAO,GAAG;IACtB,IAAI,MAAM;IACV,IAAK,IAAI,KAAK,IAAK;QACf,IAAI,IAAI,cAAc,CAAC,IAAI;YACvB,IAAI,IAAI,MAAM,EACV,OAAO;YACX,OAAO,mBAAmB,KAAK,MAAM,mBAAmB,GAAG,CAAC,EAAE;QAClE;IACJ;IACA,OAAO;AACX;AAOO,SAAS,OAAO,EAAE;IACrB,IAAI,MAAM,CAAC;IACX,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;QAC1C,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC1B,GAAG,CAAC,mBAAmB,IAAI,CAAC,EAAE,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE;IACjE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/transport.js"], "sourcesContent": ["import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;;;;;AACO,MAAM,uBAAuB;IAChC,YAAY,MAAM,EAAE,WAAW,EAAE,OAAO,CAAE;QACtC,KAAK,CAAC;QACN,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACO,MAAM,kBAAkB,gLAAA,CAAA,UAAO;IAClC;;;;;KAKC,GACD,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,WAAW;IAC3C;IACA;;;;;;;;KAQC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE;QAClC,KAAK,CAAC,aAAa,SAAS,IAAI,eAAe,QAAQ,aAAa;QACpE,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO;QACH,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM;QACX,OAAO,IAAI;IACf;IACA;;KAEC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,UAAU,KAAK,aAAa,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC7D,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,OAAO;QAChB;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,KAAK,OAAO,EAAE;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC5B,IAAI,CAAC,KAAK,CAAC;QACf,OACK;QACD,2FAA2F;QAC/F;IACJ;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,KAAK,CAAC,aAAa;IACvB;IACA;;;;;KAKC,GACD,OAAO,IAAI,EAAE;QACT,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU;QACxD,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA;;;;KAIC,GACD,SAAS,MAAM,EAAE;QACb,KAAK,CAAC,aAAa,UAAU;IACjC;IACA;;;;KAIC,GACD,QAAQ,OAAO,EAAE;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,KAAK,CAAC,aAAa,SAAS;IAChC;IACA;;;;KAIC,GACD,MAAM,OAAO,EAAE,CAAE;IACjB,UAAU,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE;QAC1B,OAAQ,SACJ,QACA,IAAI,CAAC,SAAS,KACd,IAAI,CAAC,KAAK,KACV,IAAI,CAAC,IAAI,CAAC,IAAI,GACd,IAAI,CAAC,MAAM,CAAC;IACpB;IACA,YAAY;QACR,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;QACnC,OAAO,SAAS,OAAO,CAAC,SAAS,CAAC,IAAI,WAAW,MAAM,WAAW;IACtE;IACA,QAAQ;QACJ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IACd,CAAC,AAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAC3C,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAG,GAAG;YAC3D,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;QAC/B,OACK;YACD,OAAO;QACX;IACJ;IACA,OAAO,KAAK,EAAE;QACV,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;QAC5B,OAAO,aAAa,MAAM,GAAG,MAAM,eAAe;IACtD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2867, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/transports/polling.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AACO,MAAM,gBAAgB,sKAAA,CAAA,YAAS;IAClC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,IAAI,OAAO;QACP,OAAO;IACX;IACA;;;;;KAKC,GACD,SAAS;QACL,IAAI,CAAC,KAAK;IACd;IACA;;;;;KAKC,GACD,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,QAAQ;YACV,IAAI,CAAC,UAAU,GAAG;YAClB;QACJ;QACA,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjC,IAAI,QAAQ;YACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf;gBACA,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBACtB,EAAE,SAAS;gBACf;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB;gBACA,IAAI,CAAC,IAAI,CAAC,SAAS;oBACf,EAAE,SAAS;gBACf;YACJ;QACJ,OACK;YACD;QACJ;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,OAAO,IAAI,EAAE;QACT,MAAM,WAAW,CAAC;YACd,0DAA0D;YAC1D,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ;gBACzD,IAAI,CAAC,MAAM;YACf;YACA,uDAAuD;YACvD,IAAI,YAAY,OAAO,IAAI,EAAE;gBACzB,IAAI,CAAC,OAAO,CAAC;oBAAE,aAAa;gBAAiC;gBAC7D,OAAO;YACX;YACA,iDAAiD;YACjD,IAAI,CAAC,QAAQ,CAAC;QAClB;QACA,iBAAiB;QACjB,CAAA,GAAA,kLAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC;QACpD,sCAAsC;QACtC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;YAC9B,mCAAmC;YACnC,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;gBAC5B,IAAI,CAAC,KAAK;YACd,OACK,CACL;QACJ;IACJ;IACA;;;;KAIC,GACD,UAAU;QACN,MAAM,QAAQ;YACV,IAAI,CAAC,KAAK,CAAC;gBAAC;oBAAE,MAAM;gBAAQ;aAAE;QAClC;QACA,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YAC5B;QACJ,OACK;YACD,sCAAsC;YACtC,sCAAsC;YACtC,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtB;IACJ;IACA;;;;;KAKC,GACD,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,kLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,MAAM;gBACf,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,YAAY,CAAC;YACtB;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU;QAC5C,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;QAC7B,0BAA0B;QAC1B,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACvC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;QACjD;QACA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,MAAM,GAAG,EAAE;YACpC,MAAM,GAAG,GAAG;QAChB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3020, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/contrib/has-cors.js"], "sourcesContent": ["// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;AACtD,IAAI,QAAQ;AACZ,IAAI;IACA,QAAQ,OAAO,mBAAmB,eAC9B,qBAAqB,IAAI;AACjC,EACA,OAAO,KAAK;AACR,0DAA0D;AAC1D,wBAAwB;AAC5B;AACO,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3038, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/transports/polling-xhr.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,SAAS,SAAU;AACZ,MAAM,gBAAgB,kLAAA,CAAA,UAAO;IAChC;;;;;KAKC,GACD,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,OAAO,aAAa,aAAa;YACjC,MAAM,QAAQ,aAAa,SAAS,QAAQ;YAC5C,IAAI,OAAO,SAAS,IAAI;YACxB,8CAA8C;YAC9C,IAAI,CAAC,MAAM;gBACP,OAAO,QAAQ,QAAQ;YAC3B;YACA,IAAI,CAAC,EAAE,GACH,AAAC,OAAO,aAAa,eACjB,KAAK,QAAQ,KAAK,SAAS,QAAQ,IACnC,SAAS,KAAK,IAAI;QAC9B;IACJ;IACA;;;;;;KAMC,GACD,QAAQ,IAAI,EAAE,EAAE,EAAE;QACd,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;YACrB,QAAQ;YACR,MAAM;QACV;QACA,IAAI,EAAE,CAAC,WAAW;QAClB,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW;YACxB,IAAI,CAAC,OAAO,CAAC,kBAAkB,WAAW;QAC9C;IACJ;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,IAAI,EAAE,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QACpC,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW;YACxB,IAAI,CAAC,OAAO,CAAC,kBAAkB,WAAW;QAC9C;QACA,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,gBAAgB,gLAAA,CAAA,UAAO;IAChC;;;;;KAKC,GACD,YAAY,aAAa,EAAE,GAAG,EAAE,IAAI,CAAE;QAClC,KAAK;QACL,IAAI,CAAC,aAAa,GAAG;QACrB,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG,KAAK,MAAM,IAAI;QAC9B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;QACnD,IAAI,CAAC,OAAO;IAChB;IACA;;;;KAIC,GACD,UAAU;QACN,IAAI;QACJ,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB;QAClH,KAAK,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC9B,MAAM,MAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;QAC5C,IAAI;YACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE;YAClC,IAAI;gBACA,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;oBACzB,aAAa;oBACb,IAAI,qBAAqB,IAAI,IAAI,qBAAqB,CAAC;oBACvD,IAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,CAAE;wBACnC,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI;4BAC3C,IAAI,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;wBACtD;oBACJ;gBACJ;YACJ,EACA,OAAO,GAAG,CAAE;YACZ,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE;gBACzB,IAAI;oBACA,IAAI,gBAAgB,CAAC,gBAAgB;gBACzC,EACA,OAAO,GAAG,CAAE;YAChB;YACA,IAAI;gBACA,IAAI,gBAAgB,CAAC,UAAU;YACnC,EACA,OAAO,GAAG,CAAE;YACZ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,CAAC;YAC/E,YAAY;YACZ,IAAI,qBAAqB,KAAK;gBAC1B,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;YACpD;YACA,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBAC3B,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc;YAC3C;YACA,IAAI,kBAAkB,GAAG;gBACrB,IAAI;gBACJ,IAAI,IAAI,UAAU,KAAK,GAAG;oBACtB,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAChF,aAAa;oBACb,IAAI,iBAAiB,CAAC;gBAC1B;gBACA,IAAI,MAAM,IAAI,UAAU,EACpB;gBACJ,IAAI,QAAQ,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE;oBAC3C,IAAI,CAAC,OAAO;gBAChB,OACK;oBACD,sDAAsD;oBACtD,uDAAuD;oBACvD,IAAI,CAAC,YAAY,CAAC;wBACd,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;oBAChE,GAAG;gBACP;YACJ;YACA,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK;QACvB,EACA,OAAO,GAAG;YACN,wEAAwE;YACxE,2EAA2E;YAC3E,yDAAyD;YACzD,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,QAAQ,CAAC;YAClB,GAAG;YACH;QACJ;QACA,IAAI,OAAO,aAAa,aAAa;YACjC,IAAI,CAAC,MAAM,GAAG,QAAQ,aAAa;YACnC,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QACxC;IACJ;IACA;;;;KAIC,GACD,SAAS,GAAG,EAAE;QACV,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI;QACzC,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA;;;;KAIC,GACD,SAAS,SAAS,EAAE;QAChB,IAAI,gBAAgB,OAAO,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YACxD;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG;QAC/B,IAAI,WAAW;YACX,IAAI;gBACA,IAAI,CAAC,IAAI,CAAC,KAAK;YACnB,EACA,OAAO,GAAG,CAAE;QAChB;QACA,IAAI,OAAO,aAAa,aAAa;YACjC,OAAO,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACxC;QACA,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;;;KAIC,GACD,UAAU;QACN,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;QACnC,IAAI,SAAS,MAAM;YACf,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC1B,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,CAAC,QAAQ;QACjB;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,CAAC,QAAQ;IACjB;AACJ;AACA,QAAQ,aAAa,GAAG;AACxB,QAAQ,QAAQ,GAAG,CAAC;AACpB;;;;CAIC,GACD,IAAI,OAAO,aAAa,aAAa;IACjC,aAAa;IACb,IAAI,OAAO,gBAAgB,YAAY;QACnC,aAAa;QACb,YAAY,YAAY;IAC5B,OACK,IAAI,OAAO,qBAAqB,YAAY;QAC7C,MAAM,mBAAmB,gBAAgB,oKAAA,CAAA,iBAAU,GAAG,aAAa;QACnE,iBAAiB,kBAAkB,eAAe;IACtD;AACJ;AACA,SAAS;IACL,IAAK,IAAI,KAAK,QAAQ,QAAQ,CAAE;QAC5B,IAAI,QAAQ,QAAQ,CAAC,cAAc,CAAC,IAAI;YACpC,QAAQ,QAAQ,CAAC,EAAE,CAAC,KAAK;QAC7B;IACJ;AACJ;AACA,MAAM,UAAU,AAAC;IACb,MAAM,MAAM,WAAW;QACnB,SAAS;IACb;IACA,OAAO,OAAO,IAAI,YAAY,KAAK;AACvC;AAQO,MAAM,YAAY;IACrB,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,MAAM,cAAc,QAAQ,KAAK,WAAW;QAC5C,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;IACtC;IACA,QAAQ,OAAO,CAAC,CAAC,EAAE;QACf,OAAO,MAAM,CAAC,MAAM;YAAE,IAAI,IAAI,CAAC,EAAE;QAAC,GAAG,IAAI,CAAC,IAAI;QAC9C,OAAO,IAAI,QAAQ,YAAY,IAAI,CAAC,GAAG,IAAI;IAC/C;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,MAAM,UAAU,KAAK,OAAO;IAC5B,uCAAuC;IACvC,IAAI;QACA,IAAI,gBAAgB,OAAO,kBAAkB,CAAC,CAAC,WAAW,mLAAA,CAAA,UAAO,GAAG;YAChE,OAAO,IAAI;QACf;IACJ,EACA,OAAO,GAAG,CAAE;IACZ,IAAI,CAAC,SAAS;QACV,IAAI;YACA,OAAO,IAAI,oKAAA,CAAA,iBAAU,CAAC;gBAAC;aAAS,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC;QACjE,EACA,OAAO,GAAG,CAAE;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3298, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/transports/websocket.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;AACA,iCAAiC;AACjC,MAAM,gBAAgB,OAAO,cAAc,eACvC,OAAO,UAAU,OAAO,KAAK,YAC7B,UAAU,OAAO,CAAC,WAAW,OAAO;AACjC,MAAM,eAAe,sKAAA,CAAA,YAAS;IACjC,IAAI,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS;QACrC,uGAAuG;QACvG,MAAM,OAAO,gBACP,CAAC,IACD,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,qBAAqB,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB,gBAAgB,mBAAmB,UAAU,cAAc,UAAU;QACpM,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACxB,KAAK,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY;QACzC;QACA,IAAI;YACA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,WAAW;QAChD,EACA,OAAO,KAAK;YACR,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;QACtC;QACA,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU;QAC3C,IAAI,CAAC,iBAAiB;IAC1B;IACA;;;;KAIC,GACD,oBAAoB;QAChB,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;YACb,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK;YACzB;YACA,IAAI,CAAC,MAAM;QACf;QACA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,aAAe,IAAI,CAAC,OAAO,CAAC;gBAC3C,aAAa;gBACb,SAAS;YACb;QACA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,KAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QAC/C,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,IAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB;IAC7D;IACA,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,+CAA+C;QAC/C,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,aAAa,MAAM,QAAQ,MAAM,GAAG;YAC1C,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvC,yEAAyE;gBACzE,qEAAqE;gBACrE,iBAAiB;gBACjB,IAAI;oBACA,IAAI,CAAC,OAAO,CAAC,QAAQ;gBACzB,EACA,OAAO,GAAG,CACV;gBACA,IAAI,YAAY;oBACZ,aAAa;oBACb,0DAA0D;oBAC1D,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;wBACL,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,YAAY,CAAC;oBACtB,GAAG,IAAI,CAAC,YAAY;gBACxB;YACJ;QACJ;IACJ;IACA,UAAU;QACN,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,aAAa;YAChC,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,KAAQ;YAC1B,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACd;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ;QAC1C,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;QAC7B,0BAA0B;QAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;QACjD;QACA,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,GAAG,GAAG;QAChB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;AACJ;AACA,MAAM,gBAAgB,oKAAA,CAAA,iBAAU,CAAC,SAAS,IAAI,oKAAA,CAAA,iBAAU,CAAC,YAAY;AAU9D,MAAM,WAAW;IACpB,aAAa,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;QAC/B,OAAO,CAAC,gBACF,YACI,IAAI,cAAc,KAAK,aACvB,IAAI,cAAc,OACtB,IAAI,cAAc,KAAK,WAAW;IAC5C;IACA,QAAQ,OAAO,EAAE,IAAI,EAAE;QACnB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3416, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/transports/webtransport.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AASO,MAAM,WAAW,sKAAA,CAAA,YAAS;IAC7B,IAAI,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,IAAI;YACA,aAAa;YACb,IAAI,CAAC,UAAU,GAAG,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;QACrG,EACA,OAAO,KAAK;YACR,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;QACtC;QACA,IAAI,CAAC,UAAU,CAAC,MAAM,CACjB,IAAI,CAAC;YACN,IAAI,CAAC,OAAO;QAChB,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,sBAAsB;QACvC;QACA,yFAAyF;QACzF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAC;gBAC9C,MAAM,gBAAgB,CAAA,GAAA,kLAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;gBAC/F,MAAM,SAAS,OAAO,QAAQ,CAAC,WAAW,CAAC,eAAe,SAAS;gBACnE,MAAM,gBAAgB,CAAA,GAAA,kLAAA,CAAA,4BAAyB,AAAD;gBAC9C,cAAc,QAAQ,CAAC,MAAM,CAAC,OAAO,QAAQ;gBAC7C,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,CAAC,SAAS;gBAC/C,MAAM,OAAO;oBACT,OACK,IAAI,GACJ,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;wBACtB,IAAI,MAAM;4BACN;wBACJ;wBACA,IAAI,CAAC,QAAQ,CAAC;wBACd;oBACJ,GACK,KAAK,CAAC,CAAC,OACZ;gBACJ;gBACA;gBACA,MAAM,SAAS;oBAAE,MAAM;gBAAO;gBAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;oBAChB,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/C;gBACA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAM,IAAI,CAAC,MAAM;YACrD;QACJ;IACJ;IACA,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,aAAa,MAAM,QAAQ,MAAM,GAAG;YAC1C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC;gBAC5B,IAAI,YAAY;oBACZ,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;wBACL,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,YAAY,CAAC;oBACtB,GAAG,IAAI,CAAC,YAAY;gBACxB;YACJ;QACJ;IACJ;IACA,UAAU;QACN,IAAI;QACJ,CAAC,KAAK,IAAI,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;IACxE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3496, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/transports/index.js"], "sourcesContent": ["import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,aAAa;IACtB,WAAW,oLAAA,CAAA,KAAE;IACb,cAAc,uLAAA,CAAA,KAAE;IAChB,SAAS,yLAAA,CAAA,MAAG;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3516, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/contrib/parseuri.js"], "sourcesContent": ["// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD;;;;;;;;;;;;;;;;;CAiBC;;;AACD,MAAM,KAAK;AACX,MAAM,QAAQ;IACV;IAAU;IAAY;IAAa;IAAY;IAAQ;IAAY;IAAQ;IAAQ;IAAY;IAAQ;IAAa;IAAQ;IAAS;CACxI;AACM,SAAS,MAAM,GAAG;IACrB,IAAI,IAAI,MAAM,GAAG,MAAM;QACnB,MAAM;IACV;IACA,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC;IACvD,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;QACpB,MAAM,IAAI,SAAS,CAAC,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,OAAO,IAAI,SAAS,CAAC,GAAG,IAAI,MAAM;IACpG;IACA,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,GAAG,IAAI;IAC1C,MAAO,IAAK;QACR,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI;IAC5B;IACA,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;QACpB,IAAI,MAAM,GAAG;QACb,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM;QACpE,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM;QAC9E,IAAI,OAAO,GAAG;IAClB;IACA,IAAI,SAAS,GAAG,UAAU,KAAK,GAAG,CAAC,OAAO;IAC1C,IAAI,QAAQ,GAAG,SAAS,KAAK,GAAG,CAAC,QAAQ;IACzC,OAAO;AACX;AACA,SAAS,UAAU,GAAG,EAAE,IAAI;IACxB,MAAM,OAAO,YAAY,QAAQ,KAAK,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC;IAC/D,IAAI,KAAK,KAAK,CAAC,GAAG,MAAM,OAAO,KAAK,MAAM,KAAK,GAAG;QAC9C,MAAM,MAAM,CAAC,GAAG;IACpB;IACA,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,KAAK;QACvB,MAAM,MAAM,CAAC,MAAM,MAAM,GAAG,GAAG;IACnC;IACA,OAAO;AACX;AACA,SAAS,SAAS,GAAG,EAAE,KAAK;IACxB,MAAM,OAAO,CAAC;IACd,MAAM,OAAO,CAAC,6BAA6B,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE;QAC3D,IAAI,IAAI;YACJ,IAAI,CAAC,GAAG,GAAG;QACf;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3601, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/socket.js"], "sourcesContent": ["import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;AACA,MAAM,qBAAqB,OAAO,qBAAqB,cACnD,OAAO,wBAAwB;AACnC,MAAM,0BAA0B,EAAE;AAClC,IAAI,oBAAoB;IACpB,mHAAmH;IACnH,2GAA2G;IAC3G,iBAAiB,WAAW;QACxB,wBAAwB,OAAO,CAAC,CAAC,WAAa;IAClD,GAAG;AACP;AAwBO,MAAM,6BAA6B,gLAAA,CAAA,UAAO;IAC7C;;;;;KAKC,GACD,YAAY,GAAG,EAAE,IAAI,CAAE;QACnB,KAAK;QACL,IAAI,CAAC,UAAU,GAAG,oKAAA,CAAA,oBAAiB;QACnC,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB;;;SAGC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,OAAO,aAAa,OAAO,KAAK;YAChC,OAAO;YACP,MAAM;QACV;QACA,IAAI,KAAK;YACL,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE;YACxB,KAAK,QAAQ,GAAG,UAAU,IAAI;YAC9B,KAAK,MAAM,GACP,UAAU,QAAQ,KAAK,WAAW,UAAU,QAAQ,KAAK;YAC7D,KAAK,IAAI,GAAG,UAAU,IAAI;YAC1B,IAAI,UAAU,KAAK,EACf,KAAK,KAAK,GAAG,UAAU,KAAK;QACpC,OACK,IAAI,KAAK,IAAI,EAAE;YAChB,KAAK,QAAQ,GAAG,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,EAAE,IAAI;QACzC;QACA,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,MAAM,GACP,QAAQ,KAAK,MAAM,GACb,KAAK,MAAM,GACX,OAAO,aAAa,eAAe,aAAa,SAAS,QAAQ;QAC3E,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;YAC7B,6DAA6D;YAC7D,KAAK,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ;QACtC;QACA,IAAI,CAAC,QAAQ,GACT,KAAK,QAAQ,IACT,CAAC,OAAO,aAAa,cAAc,SAAS,QAAQ,GAAG,WAAW;QAC1E,IAAI,CAAC,IAAI,GACL,KAAK,IAAI,IACL,CAAC,OAAO,aAAa,eAAe,SAAS,IAAI,GAC3C,SAAS,IAAI,GACb,IAAI,CAAC,MAAM,GACP,QACA,IAAI;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC;YACrB,MAAM,gBAAgB,EAAE,SAAS,CAAC,IAAI;YACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACrB,IAAI,CAAC,iBAAiB,CAAC,cAAc,GAAG;QAC5C;QACA,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC;YACtB,MAAM;YACN,OAAO;YACP,iBAAiB;YACjB,SAAS;YACT,gBAAgB;YAChB,iBAAiB;YACjB,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB;gBACf,WAAW;YACf;YACA,kBAAkB,CAAC;YACnB,qBAAqB;QACzB,GAAG;QACH,IAAI,CAAC,IAAI,CAAC,IAAI,GACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,MAC1B,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,EAAE;QAC9C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,UAAU;YACrC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;QAC5C;QACA,IAAI,oBAAoB;YACpB,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,6GAA6G;gBAC7G,wGAAwG;gBACxG,mBAAmB;gBACnB,IAAI,CAAC,0BAA0B,GAAG;oBAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,+BAA+B;wBAC/B,IAAI,CAAC,SAAS,CAAC,kBAAkB;wBACjC,IAAI,CAAC,SAAS,CAAC,KAAK;oBACxB;gBACJ;gBACA,iBAAiB,gBAAgB,IAAI,CAAC,0BAA0B,EAAE;YACtE;YACA,IAAI,IAAI,CAAC,QAAQ,KAAK,aAAa;gBAC/B,IAAI,CAAC,qBAAqB,GAAG;oBACzB,IAAI,CAAC,QAAQ,CAAC,mBAAmB;wBAC7B,aAAa;oBACjB;gBACJ;gBACA,wBAAwB,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAC3D;QACJ;QACA,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC3B,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,oKAAA,CAAA,kBAAe,AAAD;QACpC;QACA,IAAI,CAAC,KAAK;IACd;IACA;;;;;;KAMC,GACD,gBAAgB,IAAI,EAAE;QAClB,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;QAC/C,uCAAuC;QACvC,MAAM,GAAG,GAAG,kLAAA,CAAA,WAAQ;QACpB,iBAAiB;QACjB,MAAM,SAAS,GAAG;QAClB,oCAAoC;QACpC,IAAI,IAAI,CAAC,EAAE,EACP,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE;QACvB,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;YACtC;YACA,QAAQ,IAAI;YACZ,UAAU,IAAI,CAAC,QAAQ;YACvB,QAAQ,IAAI,CAAC,MAAM;YACnB,MAAM,IAAI,CAAC,IAAI;QACnB,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK;QACnC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;IAC5C;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;YAC9B,mDAAmD;YACnD,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,YAAY,CAAC,SAAS;YAC/B,GAAG;YACH;QACJ;QACA,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,eAAe,IAC3C,qBAAqB,qBAAqB,IAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,IACxC,cACA,IAAI,CAAC,UAAU,CAAC,EAAE;QACxB,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC;QACvC,UAAU,IAAI;QACd,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,aAAa,SAAS,EAAE;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,kBAAkB;QACrC;QACA,mBAAmB;QACnB,IAAI,CAAC,SAAS,GAAG;QACjB,6BAA6B;QAC7B,UACK,EAAE,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GACnC,EAAE,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,GACrC,EAAE,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GACnC,EAAE,CAAC,SAAS,CAAC,SAAW,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IAClE;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,qBAAqB,qBAAqB,GACtC,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI;QACvC,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,KAAK;IACd;IACA;;;;KAIC,GACD,UAAU,MAAM,EAAE;QACd,IAAI,cAAc,IAAI,CAAC,UAAU,IAC7B,WAAW,IAAI,CAAC,UAAU,IAC1B,cAAc,IAAI,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU;YAC5B,qCAAqC;YACrC,IAAI,CAAC,YAAY,CAAC;YAClB,OAAQ,OAAO,IAAI;gBACf,KAAK;oBACD,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,OAAO,IAAI;oBACvC;gBACJ,KAAK;oBACD,IAAI,CAAC,WAAW,CAAC;oBACjB,IAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,CAAC,iBAAiB;oBACtB;gBACJ,KAAK;oBACD,MAAM,MAAM,IAAI,MAAM;oBACtB,aAAa;oBACb,IAAI,IAAI,GAAG,OAAO,IAAI;oBACtB,IAAI,CAAC,QAAQ,CAAC;oBACd;gBACJ,KAAK;oBACD,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO,IAAI;oBACrC,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO,IAAI;oBACxC;YACR;QACJ,OACK,CACL;IACJ;IACA;;;;;KAKC,GACD,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,YAAY,CAAC,aAAa;QAC/B,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,GAAG;QACnC,IAAI,CAAC,aAAa,GAAG,KAAK,YAAY;QACtC,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW;QACpC,IAAI,CAAC,WAAW,GAAG,KAAK,UAAU;QAClC,IAAI,CAAC,MAAM;QACX,qCAAqC;QACrC,IAAI,aAAa,IAAI,CAAC,UAAU,EAC5B;QACJ,IAAI,CAAC,iBAAiB;IAC1B;IACA;;;;KAIC,GACD,oBAAoB;QAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB;QAC1C,MAAM,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY;QACpD,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,KAAK;QACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC;QAClB,GAAG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACrB,IAAI,CAAC,iBAAiB,CAAC,KAAK;QAChC;IACJ;IACA;;;;KAIC,GACD,WAAW;QACP,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc;QAC9C,8CAA8C;QAC9C,4DAA4D;QAC5D,8DAA8D;QAC9D,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC;QACtB,OACK;YACD,IAAI,CAAC,KAAK;QACd;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,aAAa,IAAI,CAAC,UAAU,IAC5B,IAAI,CAAC,SAAS,CAAC,QAAQ,IACvB,CAAC,IAAI,CAAC,SAAS,IACf,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACzB,MAAM,UAAU,IAAI,CAAC,mBAAmB;YACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACpB,8CAA8C;YAC9C,mDAAmD;YACnD,IAAI,CAAC,cAAc,GAAG,QAAQ,MAAM;YACpC,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA;;;;;KAKC,GACD,sBAAsB;QAClB,MAAM,yBAAyB,IAAI,CAAC,WAAW,IAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,aACxB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;QAC9B,IAAI,CAAC,wBAAwB;YACzB,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,cAAc,GAAG,oBAAoB;QACzC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI;YACrC,IAAI,MAAM;gBACN,eAAe,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE;YAC9B;YACA,IAAI,IAAI,KAAK,cAAc,IAAI,CAAC,WAAW,EAAE;gBACzC,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG;YACrC;YACA,eAAe,GAAG,0BAA0B;QAChD;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA;;;;;;;;KAQC,GACD,WAAW,GAAG,kBAAkB;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EACtB,OAAO;QACX,MAAM,aAAa,KAAK,GAAG,KAAK,IAAI,CAAC,gBAAgB;QACrD,IAAI,YAAY;YACZ,IAAI,CAAC,gBAAgB,GAAG;YACxB,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;gBACL,IAAI,CAAC,QAAQ,CAAC;YAClB,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;QACpB,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,SAAS;QAC1C,OAAO,IAAI;IACf;IACA;;;;;;;KAOC,GACD,KAAK,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;QACnB,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,SAAS;QAC1C,OAAO,IAAI;IACf;IACA;;;;;;;;KAQC,GACD,YAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACjC,IAAI,eAAe,OAAO,MAAM;YAC5B,KAAK;YACL,OAAO;QACX;QACA,IAAI,eAAe,OAAO,SAAS;YAC/B,KAAK;YACL,UAAU;QACd;QACA,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;YAC/D;QACJ;QACA,UAAU,WAAW,CAAC;QACtB,QAAQ,QAAQ,GAAG,UAAU,QAAQ,QAAQ;QAC7C,MAAM,SAAS;YACX,MAAM;YACN,MAAM;YACN,SAAS;QACb;QACA,IAAI,CAAC,YAAY,CAAC,gBAAgB;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,IAAI,IACA,IAAI,CAAC,IAAI,CAAC,SAAS;QACvB,IAAI,CAAC,KAAK;IACd;IACA;;KAEC,GACD,QAAQ;QACJ,MAAM,QAAQ;YACV,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,KAAK;QACxB;QACA,MAAM,kBAAkB;YACpB,IAAI,CAAC,GAAG,CAAC,WAAW;YACpB,IAAI,CAAC,GAAG,CAAC,gBAAgB;YACzB;QACJ;QACA,MAAM,iBAAiB;YACnB,mFAAmF;YACnF,IAAI,CAAC,IAAI,CAAC,WAAW;YACrB,IAAI,CAAC,IAAI,CAAC,gBAAgB;QAC9B;QACA,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YAC7D,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACzB,IAAI,CAAC,IAAI,CAAC,SAAS;oBACf,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB;oBACJ,OACK;wBACD;oBACJ;gBACJ;YACJ,OACK,IAAI,IAAI,CAAC,SAAS,EAAE;gBACrB;YACJ,OACK;gBACD;YACJ;QACJ;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,SAAS,GAAG,EAAE;QACV,qBAAqB,qBAAqB,GAAG;QAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KACzB,IAAI,CAAC,UAAU,KAAK,WAAW;YAC/B,IAAI,CAAC,UAAU,CAAC,KAAK;YACrB,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,IAAI,CAAC,YAAY,CAAC,SAAS;QAC3B,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IACrC;IACA;;;;KAIC,GACD,SAAS,MAAM,EAAE,WAAW,EAAE;QAC1B,IAAI,cAAc,IAAI,CAAC,UAAU,IAC7B,WAAW,IAAI,CAAC,UAAU,IAC1B,cAAc,IAAI,CAAC,UAAU,EAAE;YAC/B,eAAe;YACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB;YAC1C,6CAA6C;YAC7C,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;YAClC,mCAAmC;YACnC,IAAI,CAAC,SAAS,CAAC,KAAK;YACpB,yCAAyC;YACzC,IAAI,CAAC,SAAS,CAAC,kBAAkB;YACjC,IAAI,oBAAoB;gBACpB,IAAI,IAAI,CAAC,0BAA0B,EAAE;oBACjC,oBAAoB,gBAAgB,IAAI,CAAC,0BAA0B,EAAE;gBACzE;gBACA,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,MAAM,IAAI,wBAAwB,OAAO,CAAC,IAAI,CAAC,qBAAqB;oBACpE,IAAI,MAAM,CAAC,GAAG;wBACV,wBAAwB,MAAM,CAAC,GAAG;oBACtC;gBACJ;YACJ;YACA,kBAAkB;YAClB,IAAI,CAAC,UAAU,GAAG;YAClB,mBAAmB;YACnB,IAAI,CAAC,EAAE,GAAG;YACV,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;YACnC,0CAA0C;YAC1C,oCAAoC;YACpC,IAAI,CAAC,WAAW,GAAG,EAAE;YACrB,IAAI,CAAC,cAAc,GAAG;QAC1B;IACJ;AACJ;AACA,qBAAqB,QAAQ,GAAG,kLAAA,CAAA,WAAQ;AAwBjC,MAAM,0BAA0B;IACnC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG,EAAE;IACvB;IACA,SAAS;QACL,KAAK,CAAC;QACN,IAAI,WAAW,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;gBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACjC;QACJ;IACJ;IACA;;;;;KAKC,GACD,OAAO,IAAI,EAAE;QACT,IAAI,YAAY,IAAI,CAAC,eAAe,CAAC;QACrC,IAAI,SAAS;QACb,qBAAqB,qBAAqB,GAAG;QAC7C,MAAM,kBAAkB;YACpB,IAAI,QACA;YACJ,UAAU,IAAI,CAAC;gBAAC;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAAE;YAChD,UAAU,IAAI,CAAC,UAAU,CAAC;gBACtB,IAAI,QACA;gBACJ,IAAI,WAAW,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,EAAE;oBAC7C,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,YAAY,CAAC,aAAa;oBAC/B,IAAI,CAAC,WACD;oBACJ,qBAAqB,qBAAqB,GACtC,gBAAgB,UAAU,IAAI;oBAClC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;wBACjB,IAAI,QACA;wBACJ,IAAI,aAAa,IAAI,CAAC,UAAU,EAC5B;wBACJ;wBACA,IAAI,CAAC,YAAY,CAAC;wBAClB,UAAU,IAAI,CAAC;4BAAC;gCAAE,MAAM;4BAAU;yBAAE;wBACpC,IAAI,CAAC,YAAY,CAAC,WAAW;wBAC7B,YAAY;wBACZ,IAAI,CAAC,SAAS,GAAG;wBACjB,IAAI,CAAC,KAAK;oBACd;gBACJ,OACK;oBACD,MAAM,MAAM,IAAI,MAAM;oBACtB,aAAa;oBACb,IAAI,SAAS,GAAG,UAAU,IAAI;oBAC9B,IAAI,CAAC,YAAY,CAAC,gBAAgB;gBACtC;YACJ;QACJ;QACA,SAAS;YACL,IAAI,QACA;YACJ,+DAA+D;YAC/D,SAAS;YACT;YACA,UAAU,KAAK;YACf,YAAY;QAChB;QACA,8CAA8C;QAC9C,MAAM,UAAU,CAAC;YACb,MAAM,QAAQ,IAAI,MAAM,kBAAkB;YAC1C,aAAa;YACb,MAAM,SAAS,GAAG,UAAU,IAAI;YAChC;YACA,IAAI,CAAC,YAAY,CAAC,gBAAgB;QACtC;QACA,SAAS;YACL,QAAQ;QACZ;QACA,gDAAgD;QAChD,SAAS;YACL,QAAQ;QACZ;QACA,kDAAkD;QAClD,SAAS,UAAU,EAAE;YACjB,IAAI,aAAa,GAAG,IAAI,KAAK,UAAU,IAAI,EAAE;gBACzC;YACJ;QACJ;QACA,oDAAoD;QACpD,MAAM,UAAU;YACZ,UAAU,cAAc,CAAC,QAAQ;YACjC,UAAU,cAAc,CAAC,SAAS;YAClC,UAAU,cAAc,CAAC,SAAS;YAClC,IAAI,CAAC,GAAG,CAAC,SAAS;YAClB,IAAI,CAAC,GAAG,CAAC,aAAa;QAC1B;QACA,UAAU,IAAI,CAAC,QAAQ;QACvB,UAAU,IAAI,CAAC,SAAS;QACxB,UAAU,IAAI,CAAC,SAAS;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAC5C,SAAS,gBAAgB;YACzB,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,QAAQ;oBACT,UAAU,IAAI;gBAClB;YACJ,GAAG;QACP,OACK;YACD,UAAU,IAAI;QAClB;IACJ;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ;QACnD,KAAK,CAAC,YAAY;IACtB;IACA;;;;;KAKC,GACD,gBAAgB,QAAQ,EAAE;QACtB,MAAM,mBAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,GACpC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,EAAE;QACzC;QACA,OAAO;IACX;AACJ;AAoBO,MAAM,eAAe;IACxB,YAAY,GAAG,EAAE,OAAO,CAAC,CAAC,CAAE;QACxB,MAAM,IAAI,OAAO,QAAQ,WAAW,MAAM;QAC1C,IAAI,CAAC,EAAE,UAAU,IACZ,EAAE,UAAU,IAAI,OAAO,EAAE,UAAU,CAAC,EAAE,KAAK,UAAW;YACvD,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI;gBAAC;gBAAW;gBAAa;aAAe,EACnE,GAAG,CAAC,CAAC,gBAAkB,gLAAA,CAAA,aAAkB,CAAC,cAAc,EACxD,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC;QACzB;QACA,KAAK,CAAC,KAAK;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4213, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/transports/polling-fetch.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), <PERSON><PERSON>, <PERSON>un\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n    doPoll() {\n        this._fetch()\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data) => this.onData(data));\n        })\n            .catch((err) => {\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data)\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        })\n            .catch((err) => {\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\",\n        }).then((res) => {\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAUO,MAAM,cAAc,kLAAA,CAAA,UAAO;IAC9B,SAAS;QACL,IAAI,CAAC,MAAM,GACN,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,MAAM,EAAE;YACxD;YACA,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,OAAS,IAAI,CAAC,MAAM,CAAC;QAC1C,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,oBAAoB;QACrC;IACJ;IACA,QAAQ,IAAI,EAAE,QAAQ,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC,MACP,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAAI,MAAM,EAAE;YACzD;YACA;QACJ,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,qBAAqB;QACtC;IACJ;IACA,OAAO,IAAI,EAAE;QACT,IAAI;QACJ,MAAM,SAAS,SAAS;QACxB,MAAM,UAAU,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY;QAClD,IAAI,QAAQ;YACR,QAAQ,GAAG,CAAC,gBAAgB;QAChC;QACA,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,CAAC;QACpF,OAAO,MAAM,IAAI,CAAC,GAAG,IAAI;YACrB,QAAQ,SAAS,SAAS;YAC1B,MAAM,SAAS,OAAO;YACtB;YACA,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,YAAY;QACzD,GAAG,IAAI,CAAC,CAAC;YACL,IAAI;YACJ,yDAAyD;YACzD,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC,IAAI,OAAO,CAAC,YAAY;YAC3G,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4266, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/engine.io-client/build/esm/index.js"], "sourcesContent": ["import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n"], "names": [], "mappings": ";;;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;;;;AAXO,MAAM,WAAW,mKAAA,CAAA,SAAM,CAAC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4318, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-client/build/esm/url.js"], "sourcesContent": ["import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAUO,SAAS,IAAI,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG;IACnC,IAAI,MAAM;IACV,6BAA6B;IAC7B,MAAM,OAAQ,OAAO,aAAa,eAAe;IACjD,IAAI,QAAQ,KACR,MAAM,IAAI,QAAQ,GAAG,OAAO,IAAI,IAAI;IACxC,wBAAwB;IACxB,IAAI,OAAO,QAAQ,UAAU;QACzB,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI;YACvB,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI;gBACvB,MAAM,IAAI,QAAQ,GAAG;YACzB,OACK;gBACD,MAAM,IAAI,IAAI,GAAG;YACrB;QACJ;QACA,IAAI,CAAC,sBAAsB,IAAI,CAAC,MAAM;YAClC,IAAI,gBAAgB,OAAO,KAAK;gBAC5B,MAAM,IAAI,QAAQ,GAAG,OAAO;YAChC,OACK;gBACD,MAAM,aAAa;YACvB;QACJ;QACA,QAAQ;QACR,MAAM,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE;IAChB;IACA,4DAA4D;IAC5D,IAAI,CAAC,IAAI,IAAI,EAAE;QACX,IAAI,cAAc,IAAI,CAAC,IAAI,QAAQ,GAAG;YAClC,IAAI,IAAI,GAAG;QACf,OACK,IAAI,eAAe,IAAI,CAAC,IAAI,QAAQ,GAAG;YACxC,IAAI,IAAI,GAAG;QACf;IACJ;IACA,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI;IACvB,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;IACxC,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI;IACnD,mBAAmB;IACnB,IAAI,EAAE,GAAG,IAAI,QAAQ,GAAG,QAAQ,OAAO,MAAM,IAAI,IAAI,GAAG;IACxD,cAAc;IACd,IAAI,IAAI,GACJ,IAAI,QAAQ,GACR,QACA,OACA,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,IAAI;IAC3D,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4371, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-parser/build/esm/is-binary.js"], "sourcesContent": ["const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n"], "names": [], "mappings": ";;;;AAAA,MAAM,wBAAwB,OAAO,gBAAgB;AACrD,MAAM,SAAS,CAAC;IACZ,OAAO,OAAO,YAAY,MAAM,KAAK,aAC/B,YAAY,MAAM,CAAC,OACnB,IAAI,MAAM,YAAY;AAChC;AACA,MAAM,WAAW,OAAO,SAAS,CAAC,QAAQ;AAC1C,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,IAAI,CAAC,UAAU;AAChC,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,IAAI,CAAC,UAAU;AAMzB,SAAS,SAAS,GAAG;IACxB,OAAQ,AAAC,yBAAyB,CAAC,eAAe,eAAe,OAAO,IAAI,KACvE,kBAAkB,eAAe,QACjC,kBAAkB,eAAe;AAC1C;AACO,SAAS,UAAU,GAAG,EAAE,MAAM;IACjC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACjC,OAAO;IACX;IACA,IAAI,MAAM,OAAO,CAAC,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,IAAK;YACxC,IAAI,UAAU,GAAG,CAAC,EAAE,GAAG;gBACnB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,IAAI,IAAI,MAAM,IACV,OAAO,IAAI,MAAM,KAAK,cACtB,UAAU,MAAM,KAAK,GAAG;QACxB,OAAO,UAAU,IAAI,MAAM,IAAI;IACnC;IACA,IAAK,MAAM,OAAO,IAAK;QACnB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,QAAQ,UAAU,GAAG,CAAC,IAAI,GAAG;YACvE,OAAO;QACX;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4416, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-parser/build/esm/binary.js"], "sourcesContent": ["import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,SAAS,kBAAkB,MAAM;IACpC,MAAM,UAAU,EAAE;IAClB,MAAM,aAAa,OAAO,IAAI;IAC9B,MAAM,OAAO;IACb,KAAK,IAAI,GAAG,mBAAmB,YAAY;IAC3C,KAAK,WAAW,GAAG,QAAQ,MAAM,EAAE,iCAAiC;IACpE,OAAO;QAAE,QAAQ;QAAM,SAAS;IAAQ;AAC5C;AACA,SAAS,mBAAmB,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,MACD,OAAO;IACX,IAAI,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QAChB,MAAM,cAAc;YAAE,cAAc;YAAM,KAAK,QAAQ,MAAM;QAAC;QAC9D,QAAQ,IAAI,CAAC;QACb,OAAO;IACX,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;QAC1B,MAAM,UAAU,IAAI,MAAM,KAAK,MAAM;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,OAAO,CAAC,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE,EAAE;QAC7C;QACA,OAAO;IACX,OACK,IAAI,OAAO,SAAS,YAAY,CAAC,CAAC,gBAAgB,IAAI,GAAG;QAC1D,MAAM,UAAU,CAAC;QACjB,IAAK,MAAM,OAAO,KAAM;YACpB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM;gBACjD,OAAO,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI,EAAE;YACjD;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;AASO,SAAS,kBAAkB,MAAM,EAAE,OAAO;IAC7C,OAAO,IAAI,GAAG,mBAAmB,OAAO,IAAI,EAAE;IAC9C,OAAO,OAAO,WAAW,EAAE,mBAAmB;IAC9C,OAAO;AACX;AACA,SAAS,mBAAmB,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,MACD,OAAO;IACX,IAAI,QAAQ,KAAK,YAAY,KAAK,MAAM;QACpC,MAAM,eAAe,OAAO,KAAK,GAAG,KAAK,YACrC,KAAK,GAAG,IAAI,KACZ,KAAK,GAAG,GAAG,QAAQ,MAAM;QAC7B,IAAI,cAAc;YACd,OAAO,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,sDAAsD;QACpF,OACK;YACD,MAAM,IAAI,MAAM;QACpB;IACJ,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,IAAI,CAAC,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE,EAAE;QAC1C;IACJ,OACK,IAAI,OAAO,SAAS,UAAU;QAC/B,IAAK,MAAM,OAAO,KAAM;YACpB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM;gBACjD,IAAI,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI,EAAE;YAC9C;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4492, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-parser/build/esm/index.js"], "sourcesContent": ["import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AACA;;CAEC,GACD,MAAM,kBAAkB;IACpB;IACA;IACA;IACA;IACA;IACA;CACH;AAMM,MAAM,WAAW;AACjB,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG;IACpC,UAAU,CAAC,UAAU,CAAC,gBAAgB,GAAG,EAAE,GAAG;IAC9C,UAAU,CAAC,UAAU,CAAC,eAAe,GAAG,EAAE,GAAG;IAC7C,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;AAC/C,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAI1B,MAAM;IACT;;;;KAIC,GACD,YAAY,QAAQ,CAAE;QAClB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;;;;KAKC,GACD,OAAO,GAAG,EAAE;QACR,IAAI,IAAI,IAAI,KAAK,WAAW,KAAK,IAAI,IAAI,IAAI,KAAK,WAAW,GAAG,EAAE;YAC9D,IAAI,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,MAAM;gBAChB,OAAO,IAAI,CAAC,cAAc,CAAC;oBACvB,MAAM,IAAI,IAAI,KAAK,WAAW,KAAK,GAC7B,WAAW,YAAY,GACvB,WAAW,UAAU;oBAC3B,KAAK,IAAI,GAAG;oBACZ,MAAM,IAAI,IAAI;oBACd,IAAI,IAAI,EAAE;gBACd;YACJ;QACJ;QACA,OAAO;YAAC,IAAI,CAAC,cAAc,CAAC;SAAK;IACrC;IACA;;KAEC,GACD,eAAe,GAAG,EAAE;QAChB,gBAAgB;QAChB,IAAI,MAAM,KAAK,IAAI,IAAI;QACvB,8BAA8B;QAC9B,IAAI,IAAI,IAAI,KAAK,WAAW,YAAY,IACpC,IAAI,IAAI,KAAK,WAAW,UAAU,EAAE;YACpC,OAAO,IAAI,WAAW,GAAG;QAC7B;QACA,wCAAwC;QACxC,uCAAuC;QACvC,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,EAAE;YAC5B,OAAO,IAAI,GAAG,GAAG;QACrB;QACA,iCAAiC;QACjC,IAAI,QAAQ,IAAI,EAAE,EAAE;YAChB,OAAO,IAAI,EAAE;QACjB;QACA,YAAY;QACZ,IAAI,QAAQ,IAAI,IAAI,EAAE;YAClB,OAAO,KAAK,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,QAAQ;QACjD;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,eAAe,GAAG,EAAE;QAChB,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE;QACzC,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,MAAM;QACtD,MAAM,UAAU,eAAe,OAAO;QACtC,QAAQ,OAAO,CAAC,OAAO,4CAA4C;QACnE,OAAO,SAAS,wBAAwB;IAC5C;AACJ;AACA,8FAA8F;AAC9F,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;AACrD;AAMO,MAAM,gBAAgB,gLAAA,CAAA,UAAO;IAChC;;;;KAIC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;;;KAIC,GACD,IAAI,GAAG,EAAE;QACL,IAAI;QACJ,IAAI,OAAO,QAAQ,UAAU;YACzB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,MAAM,IAAI,MAAM;YACpB;YACA,SAAS,IAAI,CAAC,YAAY,CAAC;YAC3B,MAAM,gBAAgB,OAAO,IAAI,KAAK,WAAW,YAAY;YAC7D,IAAI,iBAAiB,OAAO,IAAI,KAAK,WAAW,UAAU,EAAE;gBACxD,OAAO,IAAI,GAAG,gBAAgB,WAAW,KAAK,GAAG,WAAW,GAAG;gBAC/D,uBAAuB;gBACvB,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAoB;gBAC7C,8DAA8D;gBAC9D,IAAI,OAAO,WAAW,KAAK,GAAG;oBAC1B,KAAK,CAAC,aAAa,WAAW;gBAClC;YACJ,OACK;gBACD,yBAAyB;gBACzB,KAAK,CAAC,aAAa,WAAW;YAClC;QACJ,OACK,IAAI,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,MAAM,EAAE;YAClC,kBAAkB;YAClB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,MAAM,IAAI,MAAM;YACpB,OACK;gBACD,SAAS,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBAC3C,IAAI,QAAQ;oBACR,wBAAwB;oBACxB,IAAI,CAAC,aAAa,GAAG;oBACrB,KAAK,CAAC,aAAa,WAAW;gBAClC;YACJ;QACJ,OACK;YACD,MAAM,IAAI,MAAM,mBAAmB;QACvC;IACJ;IACA;;;;;KAKC,GACD,aAAa,GAAG,EAAE;QACd,IAAI,IAAI;QACR,eAAe;QACf,MAAM,IAAI;YACN,MAAM,OAAO,IAAI,MAAM,CAAC;QAC5B;QACA,IAAI,UAAU,CAAC,EAAE,IAAI,CAAC,KAAK,WAAW;YAClC,MAAM,IAAI,MAAM,yBAAyB,EAAE,IAAI;QACnD;QACA,qCAAqC;QACrC,IAAI,EAAE,IAAI,KAAK,WAAW,YAAY,IAClC,EAAE,IAAI,KAAK,WAAW,UAAU,EAAE;YAClC,MAAM,QAAQ,IAAI;YAClB,MAAO,IAAI,MAAM,CAAC,EAAE,OAAO,OAAO,KAAK,IAAI,MAAM,CAAE,CAAE;YACrD,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO;YACjC,IAAI,OAAO,OAAO,QAAQ,IAAI,MAAM,CAAC,OAAO,KAAK;gBAC7C,MAAM,IAAI,MAAM;YACpB;YACA,EAAE,WAAW,GAAG,OAAO;QAC3B;QACA,6BAA6B;QAC7B,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI,IAAI;YAC3B,MAAM,QAAQ,IAAI;YAClB,MAAO,EAAE,EAAG;gBACR,MAAM,IAAI,IAAI,MAAM,CAAC;gBACrB,IAAI,QAAQ,GACR;gBACJ,IAAI,MAAM,IAAI,MAAM,EAChB;YACR;YACA,EAAE,GAAG,GAAG,IAAI,SAAS,CAAC,OAAO;QACjC,OACK;YACD,EAAE,GAAG,GAAG;QACZ;QACA,aAAa;QACb,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI;QAC5B,IAAI,OAAO,QAAQ,OAAO,SAAS,MAAM;YACrC,MAAM,QAAQ,IAAI;YAClB,MAAO,EAAE,EAAG;gBACR,MAAM,IAAI,IAAI,MAAM,CAAC;gBACrB,IAAI,QAAQ,KAAK,OAAO,MAAM,GAAG;oBAC7B,EAAE;oBACF;gBACJ;gBACA,IAAI,MAAM,IAAI,MAAM,EAChB;YACR;YACA,EAAE,EAAE,GAAG,OAAO,IAAI,SAAS,CAAC,OAAO,IAAI;QAC3C;QACA,oBAAoB;QACpB,IAAI,IAAI,MAAM,CAAC,EAAE,IAAI;YACjB,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC;YACzC,IAAI,QAAQ,cAAc,CAAC,EAAE,IAAI,EAAE,UAAU;gBACzC,EAAE,IAAI,GAAG;YACb,OACK;gBACD,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,OAAO;IACX;IACA,SAAS,GAAG,EAAE;QACV,IAAI;YACA,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO;QACvC,EACA,OAAO,GAAG;YACN,OAAO;QACX;IACJ;IACA,OAAO,eAAe,IAAI,EAAE,OAAO,EAAE;QACjC,OAAQ;YACJ,KAAK,WAAW,OAAO;gBACnB,OAAO,SAAS;YACpB,KAAK,WAAW,UAAU;gBACtB,OAAO,YAAY;YACvB,KAAK,WAAW,aAAa;gBACzB,OAAO,OAAO,YAAY,YAAY,SAAS;YACnD,KAAK,WAAW,KAAK;YACrB,KAAK,WAAW,YAAY;gBACxB,OAAQ,MAAM,OAAO,CAAC,YAClB,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,YAClB,OAAO,OAAO,CAAC,EAAE,KAAK,YACnB,gBAAgB,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAE;YAC3D,KAAK,WAAW,GAAG;YACnB,KAAK,WAAW,UAAU;gBACtB,OAAO,MAAM,OAAO,CAAC;QAC7B;IACJ;IACA;;KAEC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,sBAAsB;YACzC,IAAI,CAAC,aAAa,GAAG;QACzB;IACJ;AACJ;AACA;;;;;;;CAOC,GACD,MAAM;IACF,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;;;;KAOC,GACD,eAAe,OAAO,EAAE;QACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACpD,wBAAwB;YACxB,MAAM,SAAS,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO;YAC7D,IAAI,CAAC,sBAAsB;YAC3B,OAAO;QACX;QACA,OAAO;IACX;IACA;;KAEC,GACD,yBAAyB;QACrB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4778, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-client/build/esm/on.js"], "sourcesContent": ["export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE;IAC1B,IAAI,EAAE,CAAC,IAAI;IACX,OAAO,SAAS;QACZ,IAAI,GAAG,CAAC,IAAI;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4793, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-client/build/esm/socket.js"], "sourcesContent": ["import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA;;;CAGC,GACD,MAAM,kBAAkB,OAAO,MAAM,CAAC;IAClC,SAAS;IACT,eAAe;IACf,YAAY;IACZ,eAAe;IACf,4FAA4F;IAC5F,aAAa;IACb,gBAAgB;AACpB;AAyBO,MAAM,eAAe,gLAAA,CAAA,UAAO;IAC/B;;KAEC,GACD,YAAY,EAAE,EAAE,GAAG,EAAE,IAAI,CAAE;QACvB,KAAK;QACL;;;;;;;;;;;;;SAaC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB;;;SAGC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB;;SAEC,GACD,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB;;;;;SAKC,GACD,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB;;;SAGC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,GAAG,GAAG;QACX;;;;;;;;;;;;;;;;;;;;;;SAsBC,GACD,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,QAAQ,KAAK,IAAI,EAAE;YACnB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACzB;QACA,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG;QAC/B,IAAI,IAAI,CAAC,EAAE,CAAC,YAAY,EACpB,IAAI,CAAC,IAAI;IACjB;IACA;;;;;;;;;;;;;KAaC,GACD,IAAI,eAAe;QACf,OAAO,CAAC,IAAI,CAAC,SAAS;IAC1B;IACA;;;;KAIC,GACD,YAAY;QACR,IAAI,IAAI,CAAC,IAAI,EACT;QACJ,MAAM,KAAK,IAAI,CAAC,EAAE;QAClB,IAAI,CAAC,IAAI,GAAG;YACR,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;YACpC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;YACxC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;YACtC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;SACzC;IACL;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI;IACtB;IACA;;;;;;;;;KASC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,SAAS,EACd,OAAO,IAAI;QACf,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,EACzB,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,cAAc;QAClC,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC,WAAW,EAC9B,IAAI,CAAC,MAAM;QACf,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO;QACH,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;;;;;;;;;;;;KAcC,GACD,KAAK,GAAG,IAAI,EAAE;QACV,KAAK,OAAO,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE;QACd,IAAI,IAAI,IAAI;QACZ,IAAI,gBAAgB,cAAc,CAAC,KAAK;YACpC,MAAM,IAAI,MAAM,MAAM,GAAG,QAAQ,KAAK;QAC1C;QACA,KAAK,OAAO,CAAC;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACrE,IAAI,CAAC,WAAW,CAAC;YACjB,OAAO,IAAI;QACf;QACA,MAAM,SAAS;YACX,MAAM,kKAAA,CAAA,aAAU,CAAC,KAAK;YACtB,MAAM;QACV;QACA,OAAO,OAAO,GAAG,CAAC;QAClB,OAAO,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK;QAClD,qBAAqB;QACrB,IAAI,eAAe,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,EAAE;YAC7C,MAAM,KAAK,IAAI,CAAC,GAAG;YACnB,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,CAAC,oBAAoB,CAAC,IAAI;YAC9B,OAAO,EAAE,GAAG;QAChB;QACA,MAAM,sBAAsB,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ;QAC3J,MAAM,cAAc,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,EAAE;QACvH,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC;QAC9C,IAAI,eAAe,CACnB,OACK,IAAI,aAAa;YAClB,IAAI,CAAC,uBAAuB,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB,OACK;YACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACzB;QACA,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,OAAO,IAAI;IACf;IACA;;KAEC,GACD,qBAAqB,EAAE,EAAE,GAAG,EAAE;QAC1B,IAAI;QACJ,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU;QAChG,IAAI,YAAY,WAAW;YACvB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;YAChB;QACJ;QACA,aAAa;QACb,MAAM,QAAQ,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC;YAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,KAAK,IAAI;oBAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG;gBAC9B;YACJ;YACA,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM;QAC7B,GAAG;QACH,MAAM,KAAK,CAAC,GAAG;YACX,aAAa;YACb,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;YACvB,IAAI,KAAK,CAAC,IAAI,EAAE;QACpB;QACA,GAAG,SAAS,GAAG;QACf,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACpB;IACA;;;;;;;;;;;;;;;KAeC,GACD,YAAY,EAAE,EAAE,GAAG,IAAI,EAAE;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,MAAM,KAAK,CAAC,MAAM;gBACd,OAAO,OAAO,OAAO,QAAQ,QAAQ;YACzC;YACA,GAAG,SAAS,GAAG;YACf,KAAK,IAAI,CAAC;YACV,IAAI,CAAC,IAAI,CAAC,OAAO;QACrB;IACJ;IACA;;;;KAIC,GACD,YAAY,IAAI,EAAE;QACd,IAAI;QACJ,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,YAAY;YAC7C,MAAM,KAAK,GAAG;QAClB;QACA,MAAM,SAAS;YACX,IAAI,IAAI,CAAC,SAAS;YAClB,UAAU;YACV,SAAS;YACT;YACA,OAAO,OAAO,MAAM,CAAC;gBAAE,WAAW;YAAK,GAAG,IAAI,CAAC,KAAK;QACxD;QACA,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG;YACf,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC3B,2CAA2C;gBAC3C;YACJ;YACA,MAAM,WAAW,QAAQ;YACzB,IAAI,UAAU;gBACV,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;oBACtC,IAAI,CAAC,MAAM,CAAC,KAAK;oBACjB,IAAI,KAAK;wBACL,IAAI;oBACR;gBACJ;YACJ,OACK;gBACD,IAAI,CAAC,MAAM,CAAC,KAAK;gBACjB,IAAI,KAAK;oBACL,IAAI,SAAS;gBACjB;YACJ;YACA,OAAO,OAAO,GAAG;YACjB,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW;IACpB;IACA;;;;;KAKC,GACD,YAAY,QAAQ,KAAK,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YAC7C;QACJ;QACA,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;QAC7B,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO;YAC1B;QACJ;QACA,OAAO,OAAO,GAAG;QACjB,OAAO,QAAQ;QACf,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;QACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;IACrC;IACA;;;;;KAKC,GACD,OAAO,MAAM,EAAE;QACX,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG;QACrB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;IACpB;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,YAAY;YAChC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACP,IAAI,CAAC,kBAAkB,CAAC;YAC5B;QACJ,OACK;YACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;QACrC;IACJ;IACA;;;;;KAKC,GACD,mBAAmB,IAAI,EAAE;QACrB,IAAI,CAAC,MAAM,CAAC;YACR,MAAM,kKAAA,CAAA,aAAU,CAAC,OAAO;YACxB,MAAM,IAAI,CAAC,IAAI,GACT,OAAO,MAAM,CAAC;gBAAE,KAAK,IAAI,CAAC,IAAI;gBAAE,QAAQ,IAAI,CAAC,WAAW;YAAC,GAAG,QAC5D;QACV;IACJ;IACA;;;;;KAKC,GACD,QAAQ,GAAG,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,YAAY,CAAC,iBAAiB;QACvC;IACJ;IACA;;;;;;KAMC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE;QACzB,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI,CAAC,EAAE;QACd,IAAI,CAAC,YAAY,CAAC,cAAc,QAAQ;QACxC,IAAI,CAAC,UAAU;IACnB;IACA;;;;;KAKC,GACD,aAAa;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5B,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,OAAO,EAAE,MAAM;YAC1E,IAAI,CAAC,YAAY;gBACb,gFAAgF;gBAChF,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;gBACzB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpB,IAAI,IAAI,SAAS,EAAE;oBACf,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM;gBAC7B;YACJ;QACJ;IACJ;IACA;;;;;KAKC,GACD,SAAS,MAAM,EAAE;QACb,MAAM,gBAAgB,OAAO,GAAG,KAAK,IAAI,CAAC,GAAG;QAC7C,IAAI,CAAC,eACD;QACJ,OAAQ,OAAO,IAAI;YACf,KAAK,kKAAA,CAAA,aAAU,CAAC,OAAO;gBACnB,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE;oBAChC,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,OAAO,IAAI,CAAC,GAAG;gBACnD,OACK;oBACD,IAAI,CAAC,YAAY,CAAC,iBAAiB,IAAI,MAAM;gBACjD;gBACA;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,KAAK;YACrB,KAAK,kKAAA,CAAA,aAAU,CAAC,YAAY;gBACxB,IAAI,CAAC,OAAO,CAAC;gBACb;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,GAAG;YACnB,KAAK,kKAAA,CAAA,aAAU,CAAC,UAAU;gBACtB,IAAI,CAAC,KAAK,CAAC;gBACX;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,UAAU;gBACtB,IAAI,CAAC,YAAY;gBACjB;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,aAAa;gBACzB,IAAI,CAAC,OAAO;gBACZ,MAAM,MAAM,IAAI,MAAM,OAAO,IAAI,CAAC,OAAO;gBACzC,aAAa;gBACb,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI;gBAC3B,IAAI,CAAC,YAAY,CAAC,iBAAiB;gBACnC;QACR;IACJ;IACA;;;;;KAKC,GACD,QAAQ,MAAM,EAAE;QACZ,MAAM,OAAO,OAAO,IAAI,IAAI,EAAE;QAC9B,IAAI,QAAQ,OAAO,EAAE,EAAE;YACnB,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;QAChC;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC;QACnB,OACK;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC;QAC1C;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACjD,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,KAAK;YAC1C,KAAK,MAAM,YAAY,UAAW;gBAC9B,SAAS,KAAK,CAAC,IAAI,EAAE;YACzB;QACJ;QACA,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE;QACvB,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,UAAU;YACvE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC5C;IACJ;IACA;;;;KAIC,GACD,IAAI,EAAE,EAAE;QACJ,MAAM,OAAO,IAAI;QACjB,IAAI,OAAO;QACX,OAAO,SAAU,GAAG,IAAI;YACpB,2BAA2B;YAC3B,IAAI,MACA;YACJ,OAAO;YACP,KAAK,MAAM,CAAC;gBACR,MAAM,kKAAA,CAAA,aAAU,CAAC,GAAG;gBACpB,IAAI;gBACJ,MAAM;YACV;QACJ;IACJ;IACA;;;;;KAKC,GACD,MAAM,MAAM,EAAE;QACV,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,OAAO,QAAQ,YAAY;YAC3B;QACJ;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,0DAA0D;QAC1D,IAAI,IAAI,SAAS,EAAE;YACf,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB;QACA,aAAa;QACb,IAAI,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;IAC/B;IACA;;;;KAIC,GACD,UAAU,EAAE,EAAE,GAAG,EAAE;QACf,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK;QACtC,IAAI,CAAC,IAAI,GAAG,KAAK,uDAAuD;QACxE,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,WAAW,CAAC;IACrB;IACA;;;;KAIC,GACD,eAAe;QACX,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAS,IAAI,CAAC,SAAS,CAAC;QACpD,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACrB,IAAI,CAAC,uBAAuB,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,UAAU,GAAG,EAAE;IACxB;IACA;;;;KAIC,GACD,eAAe;QACX,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;;;;;KAMC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAe;YAClC,IAAI,CAAC,IAAI,GAAG;QAChB;QACA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI;IAC5B;IACA;;;;;;;;;;;;;;;KAeC,GACD,aAAa;QACT,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;gBAAE,MAAM,kKAAA,CAAA,aAAU,CAAC,UAAU;YAAC;QAC9C;QACA,0BAA0B;QAC1B,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,cAAc;YACd,IAAI,CAAC,OAAO,CAAC;QACjB;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,QAAQ;QACJ,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;;;;;;;;KAQC,GACD,SAAS,QAAQ,EAAE;QACf,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;KAQC,GACD,IAAI,WAAW;QACX,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;KAYC,GACD,QAAQ,OAAO,EAAE;QACb,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;QACrB,OAAO,IAAI;IACf;IACA;;;;;;;;;;KAUC,GACD,MAAM,QAAQ,EAAE;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;QAC7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACxB,OAAO,IAAI;IACf;IACA;;;;;;;;;;KAUC,GACD,WAAW,QAAQ,EAAE;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;QAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAC3B,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,OAAO,QAAQ,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI;QACf;QACA,IAAI,UAAU;YACV,MAAM,YAAY,IAAI,CAAC,aAAa;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;oBAC3B,UAAU,MAAM,CAAC,GAAG;oBACpB,OAAO,IAAI;gBACf;YACJ;QACJ,OACK;YACD,IAAI,CAAC,aAAa,GAAG,EAAE;QAC3B;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,eAAe;QACX,OAAO,IAAI,CAAC,aAAa,IAAI,EAAE;IACnC;IACA;;;;;;;;;;;;KAYC,GACD,cAAc,QAAQ,EAAE;QACpB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE;QAC7D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAChC,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;KAYC,GACD,mBAAmB,QAAQ,EAAE;QACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE;QAC7D,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;QACnC,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,eAAe,QAAQ,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,OAAO,IAAI;QACf;QACA,IAAI,UAAU;YACV,MAAM,YAAY,IAAI,CAAC,qBAAqB;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;oBAC3B,UAAU,MAAM,CAAC,GAAG;oBACpB,OAAO,IAAI;gBACf;YACJ;QACJ,OACK;YACD,IAAI,CAAC,qBAAqB,GAAG,EAAE;QACnC;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,uBAAuB;QACnB,OAAO,IAAI,CAAC,qBAAqB,IAAI,EAAE;IAC3C;IACA;;;;;;KAMC,GACD,wBAAwB,MAAM,EAAE;QAC5B,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;YACjE,MAAM,YAAY,IAAI,CAAC,qBAAqB,CAAC,KAAK;YAClD,KAAK,MAAM,YAAY,UAAW;gBAC9B,SAAS,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;YACpC;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5604, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-client/build/esm/contrib/backo2.js"], "sourcesContent": ["/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AACM,SAAS,QAAQ,IAAI;IACxB,OAAO,QAAQ,CAAC;IAChB,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI;IACtB,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,IAAI;IACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;IAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG;IAClE,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA;;;;;CAKC,GACD,QAAQ,SAAS,CAAC,QAAQ,GAAG;IACzB,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ;IACtD,IAAI,IAAI,CAAC,MAAM,EAAE;QACb,IAAI,OAAO,KAAK,MAAM;QACtB,IAAI,YAAY,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG;QAChD,KAAK,CAAC,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,KAAK,IAAI,KAAK,YAAY,KAAK;IAClE;IACA,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI;AACpC;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,KAAK,GAAG;IACtB,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;IACpC,IAAI,CAAC,EAAE,GAAG;AACd;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;IACpC,IAAI,CAAC,GAAG,GAAG;AACf;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;IAC1C,IAAI,CAAC,MAAM,GAAG;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5673, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-client/build/esm/manager.js"], "sourcesContent": ["import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,MAAM,gBAAgB,gLAAA,CAAA,UAAO;IAChC,YAAY,GAAG,EAAE,IAAI,CAAE;QACnB,IAAI;QACJ,KAAK;QACL,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,OAAO,aAAa,OAAO,KAAK;YAChC,OAAO;YACP,MAAM;QACV;QACA,OAAO,QAAQ,CAAC;QAChB,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,YAAY,KAAK;QACxC,IAAI,CAAC,oBAAoB,CAAC,KAAK,oBAAoB,IAAI;QACvD,IAAI,CAAC,iBAAiB,CAAC,KAAK,iBAAiB,IAAI;QACjD,IAAI,CAAC,oBAAoB,CAAC,KAAK,oBAAoB,IAAI;QACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,KAAK,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC1F,IAAI,CAAC,OAAO,GAAG,IAAI,8KAAA,CAAA,UAAO,CAAC;YACvB,KAAK,IAAI,CAAC,iBAAiB;YAC3B,KAAK,IAAI,CAAC,oBAAoB;YAC9B,QAAQ,IAAI,CAAC,mBAAmB;QACpC;QACA,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,GAAG,QAAQ,KAAK,OAAO;QACxD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,GAAG,GAAG;QACX,MAAM,UAAU,KAAK,MAAM,IAAI;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,OAAO;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,OAAO;QAClC,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW,KAAK;QACzC,IAAI,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,IAAI;IACjB;IACA,aAAa,CAAC,EAAE;QACZ,IAAI,CAAC,UAAU,MAAM,EACjB,OAAO,IAAI,CAAC,aAAa;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG;YACJ,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,OAAO,IAAI;IACf;IACA,qBAAqB,CAAC,EAAE;QACpB,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,qBAAqB;QACrC,IAAI,CAAC,qBAAqB,GAAG;QAC7B,OAAO,IAAI;IACf;IACA,kBAAkB,CAAC,EAAE;QACjB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,kBAAkB;QAClC,IAAI,CAAC,kBAAkB,GAAG;QAC1B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;QACnE,OAAO,IAAI;IACf;IACA,oBAAoB,CAAC,EAAE;QACnB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,oBAAoB;QACpC,IAAI,CAAC,oBAAoB,GAAG;QAC5B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC;QACtE,OAAO,IAAI;IACf;IACA,qBAAqB,CAAC,EAAE;QACpB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,qBAAqB;QACrC,IAAI,CAAC,qBAAqB,GAAG;QAC7B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;QACnE,OAAO,IAAI;IACf;IACA,QAAQ,CAAC,EAAE;QACP,IAAI,CAAC,UAAU,MAAM,EACjB,OAAO,IAAI,CAAC,QAAQ;QACxB,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,uBAAuB;QACnB,gEAAgE;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IACnB,IAAI,CAAC,aAAa,IAClB,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG;YAC7B,sEAAsE;YACtE,IAAI,CAAC,SAAS;QAClB;IACJ;IACA;;;;;;KAMC,GACD,KAAK,EAAE,EAAE;QACL,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAC1B,OAAO,IAAI;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,mKAAA,CAAA,SAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI;QAC5C,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG;QACrB,cAAc;QACd,MAAM,iBAAiB,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ;YACtC,KAAK,MAAM;YACX,MAAM;QACV;QACA,MAAM,UAAU,CAAC;YACb,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,YAAY,CAAC,SAAS;YAC3B,IAAI,IAAI;gBACJ,GAAG;YACP,OACK;gBACD,qDAAqD;gBACrD,IAAI,CAAC,oBAAoB;YAC7B;QACJ;QACA,eAAe;QACf,MAAM,WAAW,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS;QACrC,IAAI,UAAU,IAAI,CAAC,QAAQ,EAAE;YACzB,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,YAAY;YACZ,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC;gBAC5B;gBACA,QAAQ,IAAI,MAAM;gBAClB,OAAO,KAAK;YAChB,GAAG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,MAAM,KAAK;YACf;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,IAAI,CAAC,cAAc,CAAC;YACxB;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,QAAQ,EAAE,EAAE;QACR,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB;IACA;;;;KAIC,GACD,SAAS;QACL,iBAAiB;QACjB,IAAI,CAAC,OAAO;QACZ,eAAe;QACf,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC;QAClB,eAAe;QACf,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAC/L,aAAa;QACb,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IACxD;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,OAAO,IAAI,EAAE;QACT,IAAI;YACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACrB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,OAAO,CAAC,eAAe;QAChC;IACJ;IACA;;;;KAIC,GACD,UAAU,MAAM,EAAE;QACd,mIAAmI;QACnI,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;YACL,IAAI,CAAC,YAAY,CAAC,UAAU;QAChC,GAAG,IAAI,CAAC,YAAY;IACxB;IACA;;;;KAIC,GACD,QAAQ,GAAG,EAAE;QACT,IAAI,CAAC,YAAY,CAAC,SAAS;IAC/B;IACA;;;;;KAKC,GACD,OAAO,GAAG,EAAE,IAAI,EAAE;QACd,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;QAC3B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI,mKAAA,CAAA,SAAM,CAAC,IAAI,EAAE,KAAK;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;QACrB,OACK,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,MAAM,EAAE;YAC1C,OAAO,OAAO;QAClB;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,SAAS,MAAM,EAAE;QACb,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QAClC,KAAK,MAAM,OAAO,KAAM;YACpB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;YAC7B,IAAI,OAAO,MAAM,EAAE;gBACf;YACJ;QACJ;QACA,IAAI,CAAC,MAAM;IACf;IACA;;;;;KAKC,GACD,QAAQ,MAAM,EAAE;QACZ,MAAM,iBAAiB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,EAAE,OAAO,OAAO;QACvD;IACJ;IACA;;;;KAIC,GACD,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAe;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QACnB,IAAI,CAAC,OAAO,CAAC,OAAO;IACxB;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;;;KAIC,GACD,aAAa;QACT,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;;;;;;;KAQC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE;QACzB,IAAI;QACJ,IAAI,CAAC,OAAO;QACZ,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QAChE,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;QACnC,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAC3C,IAAI,CAAC,SAAS;QAClB;IACJ;IACA;;;;KAIC,GACD,YAAY;QACR,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EACxC,OAAO,IAAI;QACf,MAAM,OAAO,IAAI;QACjB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACrD,IAAI,CAAC,OAAO,CAAC,KAAK;YAClB,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG;QACzB,OACK;YACD,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ;YACnC,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC;gBAC5B,IAAI,KAAK,aAAa,EAClB;gBACJ,IAAI,CAAC,YAAY,CAAC,qBAAqB,KAAK,OAAO,CAAC,QAAQ;gBAC5D,yDAAyD;gBACzD,IAAI,KAAK,aAAa,EAClB;gBACJ,KAAK,IAAI,CAAC,CAAC;oBACP,IAAI,KAAK;wBACL,KAAK,aAAa,GAAG;wBACrB,KAAK,SAAS;wBACd,IAAI,CAAC,YAAY,CAAC,mBAAmB;oBACzC,OACK;wBACD,KAAK,WAAW;oBACpB;gBACJ;YACJ,GAAG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,MAAM,KAAK;YACf;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,IAAI,CAAC,cAAc,CAAC;YACxB;QACJ;IACJ;IACA;;;;KAIC,GACD,cAAc;QACV,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,YAAY,CAAC,aAAa;IACnC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6022, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/node_modules/socket.io-client/build/esm/index.js"], "sourcesContent": ["import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AA2CA;;;;CAIC,GACD;AAOA;;;;AAtDA;;CAEC,GACD,MAAM,QAAQ,CAAC;AACf,SAAS,OAAO,GAAG,EAAE,IAAI;IACrB,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;QACP,MAAM;IACV;IACA,OAAO,QAAQ,CAAC;IAChB,MAAM,SAAS,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,IAAI,IAAI;IACrC,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,gBAAgB,KAAK,CAAC,GAAG,IAAI,QAAQ,KAAK,CAAC,GAAG,CAAC,OAAO;IAC5D,MAAM,gBAAgB,KAAK,QAAQ,IAC/B,IAAI,CAAC,uBAAuB,IAC5B,UAAU,KAAK,SAAS,IACxB;IACJ,IAAI;IACJ,IAAI,eAAe;QACf,KAAK,IAAI,oKAAA,CAAA,UAAO,CAAC,QAAQ;IAC7B,OACK;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACZ,KAAK,CAAC,GAAG,GAAG,IAAI,oKAAA,CAAA,UAAO,CAAC,QAAQ;QACpC;QACA,KAAK,KAAK,CAAC,GAAG;IAClB;IACA,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE;QAC7B,KAAK,KAAK,GAAG,OAAO,QAAQ;IAChC;IACA,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE;AAClC;AACA,4EAA4E;AAC5E,iEAAiE;AACjE,OAAO,MAAM,CAAC,QAAQ;IAClB,SAAA,oKAAA,CAAA,UAAO;IACP,QAAA,mKAAA,CAAA,SAAM;IACN,IAAI;IACJ,SAAS;AACb", "ignoreList": [0], "debugId": null}}]}