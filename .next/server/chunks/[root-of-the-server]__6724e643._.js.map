{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/lib/mongodb.ts"], "sourcesContent": ["import { MongoClient, Db } from 'mongodb';\n\nif (!process.env.MONGODB_URI) {\n  throw new Error('Invalid/Missing environment variable: \"MONGODB_URI\"');\n}\n\nconst uri = process.env.MONGODB_URI;\nconst options = {};\n\nlet client: MongoClient;\nlet clientPromise: Promise<MongoClient>;\n\nif (process.env.NODE_ENV === 'development') {\n  // In development mode, use a global variable so that the value\n  // is preserved across module reloads caused by HMR (Hot Module Replacement).\n  let globalWithMongo = global as typeof globalThis & {\n    _mongoClientPromise?: Promise<MongoClient>;\n  };\n\n  if (!globalWithMongo._mongoClientPromise) {\n    client = new MongoClient(uri, options);\n    globalWithMongo._mongoClientPromise = client.connect();\n  }\n  clientPromise = globalWithMongo._mongoClientPromise;\n} else {\n  // In production mode, it's best to not use a global variable.\n  client = new MongoClient(uri, options);\n  clientPromise = client.connect();\n}\n\n// Export a module-scoped MongoClient promise. By doing this in a\n// separate module, the client can be shared across functions.\nexport default clientPromise;\n\nexport async function getDatabase(): Promise<Db> {\n  const client = await clientPromise;\n  return client.db('instagramclone');\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW,EAAE;IAC5B,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,MAAM,QAAQ,GAAG,CAAC,WAAW;AACnC,MAAM,UAAU,CAAC;AAEjB,IAAI;AACJ,IAAI;AAEJ,wCAA4C;IAC1C,+DAA+D;IAC/D,6EAA6E;IAC7E,IAAI,kBAAkB;IAItB,IAAI,CAAC,gBAAgB,mBAAmB,EAAE;QACxC,SAAS,IAAI,uGAAA,CAAA,cAAW,CAAC,KAAK;QAC9B,gBAAgB,mBAAmB,GAAG,OAAO,OAAO;IACtD;IACA,gBAAgB,gBAAgB,mBAAmB;AACrD,OAAO;;AAIP;uCAIe;AAER,eAAe;IACpB,MAAM,SAAS,MAAM;IACrB,OAAO,OAAO,EAAE,CAAC;AACnB", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { MongoDBAdapter } from '@next-auth/mongodb-adapter';\nimport bcrypt from 'bcryptjs';\nimport clientPromise, { getDatabase } from './mongodb';\nimport { User } from '@/models/User';\n\nexport const authOptions: NextAuthOptions = {\n  adapter: MongoDBAdapter(clientPromise),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          const db = await getDatabase();\n          const user = await db.collection<User>('users').findOne({\n            email: credentials.email,\n          });\n\n          if (!user) {\n            return null;\n          }\n\n          const isPasswordValid = await bcrypt.compare(\n            credentials.password,\n            user.password\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          return {\n            id: user._id!.toString(),\n            email: user.email,\n            name: user.fullName,\n            username: user.username,\n            image: user.profilePicture,\n          };\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.username = (user as any).username;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        (session.user as any).username = token.username;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n  },\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAGO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,uKAAA,CAAA,iBAAc,AAAD,EAAE,uHAAA,CAAA,UAAa;IACrC,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,KAAK,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD;oBAC3B,MAAM,OAAO,MAAM,GAAG,UAAU,CAAO,SAAS,OAAO,CAAC;wBACtD,OAAO,YAAY,KAAK;oBAC1B;oBAEA,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;oBAGf,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,GAAG,CAAE,QAAQ;wBACtB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,QAAQ;wBACnB,UAAU,KAAK,QAAQ;wBACvB,OAAO,KAAK,cAAc;oBAC5B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,QAAQ,GAAG,AAAC,KAAa,QAAQ;YACzC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC1B,QAAQ,IAAI,CAAS,QAAQ,GAAG,MAAM,QAAQ;YACjD;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}