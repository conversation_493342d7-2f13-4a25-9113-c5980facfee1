{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { User, UserProfile } from '@/models/User';\nimport { Post } from '@/models/Post';\nimport { Conversation, Message } from '@/models/Message';\n\ninterface AppState {\n  // User state\n  currentUser: User | null;\n  setCurrentUser: (user: User | null) => void;\n  \n  // Posts state\n  posts: Post[];\n  setPosts: (posts: Post[]) => void;\n  addPost: (post: Post) => void;\n  updatePost: (postId: string, updates: Partial<Post>) => void;\n  \n  // Stories state\n  stories: any[];\n  setStories: (stories: any[]) => void;\n  \n  // Messages state\n  conversations: Conversation[];\n  setConversations: (conversations: Conversation[]) => void;\n  activeConversation: Conversation | null;\n  setActiveConversation: (conversation: Conversation | null) => void;\n  messages: Message[];\n  setMessages: (messages: Message[]) => void;\n  addMessage: (message: Message) => void;\n  \n  // UI state\n  isLoading: boolean;\n  setIsLoading: (loading: boolean) => void;\n  showCreatePost: boolean;\n  setShowCreatePost: (show: boolean) => void;\n  showStoryViewer: boolean;\n  setShowStoryViewer: (show: boolean) => void;\n  \n  // Search state\n  searchResults: UserProfile[];\n  setSearchResults: (results: UserProfile[]) => void;\n  \n  // Notifications state\n  notifications: any[];\n  setNotifications: (notifications: any[]) => void;\n  unreadCount: number;\n  setUnreadCount: (count: number) => void;\n}\n\nexport const useStore = create<AppState>((set, get) => ({\n  // User state\n  currentUser: null,\n  setCurrentUser: (user) => set({ currentUser: user }),\n  \n  // Posts state\n  posts: [],\n  setPosts: (posts) => set({ posts }),\n  addPost: (post) => set((state) => ({ posts: [post, ...state.posts] })),\n  updatePost: (postId, updates) => set((state) => ({\n    posts: state.posts.map((post) =>\n      post._id?.toString() === postId ? { ...post, ...updates } : post\n    ),\n  })),\n  \n  // Stories state\n  stories: [],\n  setStories: (stories) => set({ stories }),\n  \n  // Messages state\n  conversations: [],\n  setConversations: (conversations) => set({ conversations }),\n  activeConversation: null,\n  setActiveConversation: (conversation) => set({ activeConversation: conversation }),\n  messages: [],\n  setMessages: (messages) => set({ messages }),\n  addMessage: (message) => set((state) => ({ messages: [...state.messages, message] })),\n  \n  // UI state\n  isLoading: false,\n  setIsLoading: (loading) => set({ isLoading: loading }),\n  showCreatePost: false,\n  setShowCreatePost: (show) => set({ showCreatePost: show }),\n  showStoryViewer: false,\n  setShowStoryViewer: (show) => set({ showStoryViewer: show }),\n  \n  // Search state\n  searchResults: [],\n  setSearchResults: (results) => set({ searchResults: results }),\n  \n  // Notifications state\n  notifications: [],\n  setNotifications: (notifications) => set({ notifications }),\n  unreadCount: 0,\n  setUnreadCount: (count) => set({ unreadCount: count }),\n}));\n"], "names": [], "mappings": ";;;AAAA;;AAgDO,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAY,CAAC,KAAK,MAAQ,CAAC;QACtD,aAAa;QACb,aAAa;QACb,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAElD,cAAc;QACd,OAAO,EAAE;QACT,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,SAAS,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBAAE,OAAO;wBAAC;2BAAS,MAAM,KAAK;qBAAC;gBAAC,CAAC;QACpE,YAAY,CAAC,QAAQ,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC/C,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OACtB,KAAK,GAAG,EAAE,eAAe,SAAS;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEhE,CAAC;QAED,gBAAgB;QAChB,SAAS,EAAE;QACX,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QAEvC,iBAAiB;QACjB,eAAe,EAAE;QACjB,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QACzD,oBAAoB;QACpB,uBAAuB,CAAC,eAAiB,IAAI;gBAAE,oBAAoB;YAAa;QAChF,UAAU,EAAE;QACZ,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAC1C,YAAY,CAAC,UAAY,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAEnF,WAAW;QACX,WAAW;QACX,cAAc,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QACpD,gBAAgB;QAChB,mBAAmB,CAAC,OAAS,IAAI;gBAAE,gBAAgB;YAAK;QACxD,iBAAiB;QACjB,oBAAoB,CAAC,OAAS,IAAI;gBAAE,iBAAiB;YAAK;QAE1D,eAAe;QACf,eAAe,EAAE;QACjB,kBAAkB,CAAC,UAAY,IAAI;gBAAE,eAAe;YAAQ;QAE5D,sBAAsB;QACtB,eAAe,EAAE;QACjB,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QACzD,aAAa;QACb,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;IACtD,CAAC", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useSession, signOut } from 'next-auth/react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport {\n  HomeIcon,\n  MagnifyingGlassIcon,\n  PlusCircleIcon,\n  HeartIcon,\n  UserIcon,\n  ChatBubbleLeftIcon,\n  FilmIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n} from '@heroicons/react/24/outline';\nimport {\n  HomeIcon as HomeIconSolid,\n  MagnifyingGlassIcon as SearchIconSolid,\n  HeartIcon as HeartIconSolid,\n  UserIcon as UserIconSolid,\n  ChatBubbleLeftIcon as MessageIconSolid,\n  FilmIcon as ReelsIconSolid,\n} from '@heroicons/react/24/solid';\nimport { useStore } from '@/store/useStore';\n\nconst navigation = [\n  { name: 'Home', href: '/', icon: HomeIcon, activeIcon: HomeIconSolid },\n  { name: 'Search', href: '/search', icon: MagnifyingGlassIcon, activeIcon: SearchIconSolid },\n  { name: 'Explore', href: '/explore', icon: FilmIcon, activeIcon: ReelsIconSolid },\n  { name: '<PERSON><PERSON>', href: '/reels', icon: FilmIcon, activeIcon: ReelsIconSolid },\n  { name: 'Messages', href: '/messages', icon: ChatBubbleLeftIcon, activeIcon: MessageIconSolid },\n  { name: 'Notifications', href: '/notifications', icon: HeartIcon, activeIcon: HeartIconSolid },\n  { name: 'Profile', href: '/profile', icon: UserIcon, activeIcon: UserIconSolid },\n];\n\nexport default function Sidebar() {\n  const { data: session } = useSession();\n  const pathname = usePathname();\n  const { setShowCreatePost, unreadCount } = useStore();\n\n  const handleCreatePost = () => {\n    setShowCreatePost(true);\n  };\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: '/auth/signin' });\n  };\n\n  return (\n    <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4 shadow-sm\">\n      <div className=\"flex h-16 shrink-0 items-center\">\n        <h1 className=\"text-2xl font-bold instagram-gradient bg-clip-text text-transparent\">\n          Instagram\n        </h1>\n      </div>\n      <nav className=\"flex flex-1 flex-col\">\n        <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n          <li>\n            <ul role=\"list\" className=\"-mx-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href;\n                const Icon = isActive ? item.activeIcon : item.icon;\n                \n                return (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className={`group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors ${\n                        isActive\n                          ? 'bg-gray-50 text-gray-900'\n                          : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'\n                      }`}\n                    >\n                      <div className=\"relative\">\n                        <Icon className=\"h-6 w-6 shrink-0\" aria-hidden=\"true\" />\n                        {item.name === 'Notifications' && unreadCount > 0 && (\n                          <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                            {unreadCount > 9 ? '9+' : unreadCount}\n                          </span>\n                        )}\n                      </div>\n                      {item.name}\n                    </Link>\n                  </li>\n                );\n              })}\n              \n              {/* Create Post Button */}\n              <li>\n                <button\n                  onClick={handleCreatePost}\n                  className=\"group flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n                >\n                  <PlusCircleIcon className=\"h-6 w-6 shrink-0\" aria-hidden=\"true\" />\n                  Create\n                </button>\n              </li>\n            </ul>\n          </li>\n          \n          {/* Bottom Section */}\n          <li className=\"mt-auto\">\n            <ul role=\"list\" className=\"-mx-2 space-y-1\">\n              <li>\n                <Link\n                  href=\"/settings\"\n                  className=\"group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n                >\n                  <Cog6ToothIcon className=\"h-6 w-6 shrink-0\" aria-hidden=\"true\" />\n                  Settings\n                </Link>\n              </li>\n              <li>\n                <button\n                  onClick={handleSignOut}\n                  className=\"group flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-colors\"\n                >\n                  <ArrowRightOnRectangleIcon className=\"h-6 w-6 shrink-0\" aria-hidden=\"true\" />\n                  Sign out\n                </button>\n              </li>\n            </ul>\n          </li>\n        </ul>\n      </nav>\n      \n      {/* User Profile Section */}\n      {session?.user && (\n        <div className=\"flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-gray-900\">\n          <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n            {session.user.image ? (\n              <img\n                className=\"h-8 w-8 rounded-full\"\n                src={session.user.image}\n                alt={session.user.name || ''}\n              />\n            ) : (\n              <UserIcon className=\"h-5 w-5 text-gray-600\" />\n            )}\n          </div>\n          <span className=\"sr-only\">Your profile</span>\n          <span aria-hidden=\"true\" className=\"truncate\">\n            {session.user.name}\n          </span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAxBA;;;;;;;;AA0BA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;QAAE,YAAY,6MAAA,CAAA,WAAa;IAAC;IACrE;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,qOAAA,CAAA,sBAAmB;QAAE,YAAY,mOAAA,CAAA,sBAAe;IAAC;IAC1F;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,+MAAA,CAAA,WAAQ;QAAE,YAAY,6MAAA,CAAA,WAAc;IAAC;IAChF;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,+MAAA,CAAA,WAAQ;QAAE,YAAY,6MAAA,CAAA,WAAc;IAAC;IAC5E;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,mOAAA,CAAA,qBAAkB;QAAE,YAAY,iOAAA,CAAA,qBAAgB;IAAC;IAC9F;QAAE,MAAM;QAAiB,MAAM;QAAkB,MAAM,iNAAA,CAAA,YAAS;QAAE,YAAY,+MAAA,CAAA,YAAc;IAAC;IAC7F;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,+MAAA,CAAA,WAAQ;QAAE,YAAY,6MAAA,CAAA,WAAa;IAAC;CAChF;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAElD,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,MAAM,gBAAgB;QACpB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAe;IACxC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAsE;;;;;;;;;;;0BAItF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,MAAK;oBAAO,WAAU;;sCACxB,8OAAC;sCACC,cAAA,8OAAC;gCAAG,MAAK;gCAAO,WAAU;;oCACvB,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,MAAM,OAAO,WAAW,KAAK,UAAU,GAAG,KAAK,IAAI;wCAEnD,qBACE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAW,CAAC,oFAAoF,EAC9F,WACI,6BACA,sDACJ;;kEAEF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;gEAAmB,eAAY;;;;;;4DAC9C,KAAK,IAAI,KAAK,mBAAmB,cAAc,mBAC9C,8OAAC;gEAAK,WAAU;0EACb,cAAc,IAAI,OAAO;;;;;;;;;;;;oDAI/B,KAAK,IAAI;;;;;;;2CAjBL,KAAK,IAAI;;;;;oCAqBtB;kDAGA,8OAAC;kDACC,cAAA,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,2NAAA,CAAA,iBAAc;oDAAC,WAAU;oDAAmB,eAAY;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;;;sCAQ1E,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAG,MAAK;gCAAO,WAAU;;kDACxB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,yNAAA,CAAA,gBAAa;oDAAC,WAAU;oDAAmB,eAAY;;;;;;gDAAS;;;;;;;;;;;;kDAIrE,8OAAC;kDACC,cAAA,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,iPAAA,CAAA,4BAAyB;oDAAC,WAAU;oDAAmB,eAAY;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUxF,SAAS,sBACR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,IAAI,CAAC,KAAK,iBACjB,8OAAC;4BACC,WAAU;4BACV,KAAK,QAAQ,IAAI,CAAC,KAAK;4BACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;;;;;iDAG5B,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAGxB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC;wBAAK,eAAY;wBAAO,WAAU;kCAChC,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/layout/MobileNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport {\n  HomeIcon,\n  MagnifyingGlassIcon,\n  PlusCircleIcon,\n  HeartIcon,\n  UserIcon,\n} from '@heroicons/react/24/outline';\nimport {\n  HomeIcon as HomeIconSolid,\n  MagnifyingGlassIcon as SearchIconSolid,\n  HeartIcon as HeartIconSolid,\n  UserIcon as UserIconSolid,\n} from '@heroicons/react/24/solid';\nimport { useStore } from '@/store/useStore';\n\nconst navigation = [\n  { name: 'Home', href: '/', icon: HomeIcon, activeIcon: HomeIconSolid },\n  { name: 'Search', href: '/search', icon: MagnifyingGlassIcon, activeIcon: SearchIconSolid },\n  { name: 'Create', href: '#', icon: PlusCircleIcon, activeIcon: PlusCircleIcon, isButton: true },\n  { name: 'Notifications', href: '/notifications', icon: HeartIcon, activeIcon: HeartIconSolid },\n  { name: 'Profile', href: '/profile', icon: UserIcon, activeIcon: UserIconSolid },\n];\n\nexport default function MobileNavigation() {\n  const pathname = usePathname();\n  const { setShowCreatePost, unreadCount } = useStore();\n\n  const handleCreatePost = () => {\n    setShowCreatePost(true);\n  };\n\n  return (\n    <>\n      {/* Top Header */}\n      <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6\">\n        <div className=\"flex flex-1 items-center justify-between\">\n          <h1 className=\"text-xl font-bold instagram-gradient bg-clip-text text-transparent\">\n            Instagram\n          </h1>\n          <div className=\"flex items-center gap-x-4\">\n            <Link\n              href=\"/messages\"\n              className=\"text-gray-700 hover:text-gray-900\"\n            >\n              <svg\n                className=\"h-6 w-6\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                strokeWidth={1.5}\n                stroke=\"currentColor\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z\"\n                />\n              </svg>\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Navigation */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200\">\n        <nav className=\"flex\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href;\n            const Icon = isActive ? item.activeIcon : item.icon;\n\n            if (item.isButton) {\n              return (\n                <button\n                  key={item.name}\n                  onClick={handleCreatePost}\n                  className=\"flex-1 flex flex-col items-center justify-center py-2 px-1 text-gray-700 hover:text-gray-900\"\n                >\n                  <Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </button>\n              );\n            }\n\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`flex-1 flex flex-col items-center justify-center py-2 px-1 transition-colors ${\n                  isActive\n                    ? 'text-gray-900'\n                    : 'text-gray-700 hover:text-gray-900'\n                }`}\n              >\n                <div className=\"relative\">\n                  <Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  {item.name === 'Notifications' && unreadCount > 0 && (\n                    <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                      {unreadCount > 9 ? '9+' : unreadCount}\n                    </span>\n                  )}\n                </div>\n              </Link>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Bottom padding to account for fixed navigation */}\n      <div className=\"h-16\" />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AAAA;AAAA;AAMA;AAjBA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;QAAE,YAAY,6MAAA,CAAA,WAAa;IAAC;IACrE;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,qOAAA,CAAA,sBAAmB;QAAE,YAAY,mOAAA,CAAA,sBAAe;IAAC;IAC1F;QAAE,MAAM;QAAU,MAAM;QAAK,MAAM,2NAAA,CAAA,iBAAc;QAAE,YAAY,2NAAA,CAAA,iBAAc;QAAE,UAAU;IAAK;IAC9F;QAAE,MAAM;QAAiB,MAAM;QAAkB,MAAM,iNAAA,CAAA,YAAS;QAAE,YAAY,+MAAA,CAAA,YAAc;IAAC;IAC7F;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,+MAAA,CAAA,WAAQ;QAAE,YAAY,6MAAA,CAAA,WAAa;IAAC;CAChF;AAEc,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAElD,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqE;;;;;;sCAGnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,aAAa;oCACb,QAAO;8CAEP,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,MAAM,OAAO,WAAW,KAAK,UAAU,GAAG,KAAK,IAAI;wBAEnD,IAAI,KAAK,QAAQ,EAAE;4BACjB,qBACE,8OAAC;gCAEC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAK,WAAU;oCAAU,eAAY;;;;;;+BAJjC,KAAK,IAAI;;;;;wBAOpB;wBAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAC,6EAA6E,EACvF,WACI,kBACA,qCACJ;sCAEF,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;wCAAU,eAAY;;;;;;oCACrC,KAAK,IAAI,KAAK,mBAAmB,cAAc,mBAC9C,8OAAC;wCAAK,WAAU;kDACb,cAAc,IAAI,OAAO;;;;;;;;;;;;2BAZ3B,KAAK,IAAI;;;;;oBAkBpB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/modals/CreatePostModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';\nimport { useStore } from '@/store/useStore';\n\nexport default function CreatePostModal() {\n  const { setShowCreatePost, addPost } = useStore();\n  const [step, setStep] = useState(1); // 1: Select files, 2: Edit, 3: Share\n  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\n  const [previews, setPreviews] = useState<string[]>([]);\n  const [caption, setCaption] = useState('');\n  const [location, setLocation] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleClose = () => {\n    setShowCreatePost(false);\n    setStep(1);\n    setSelectedFiles([]);\n    setPreviews([]);\n    setCaption('');\n    setLocation('');\n  };\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(e.target.files || []);\n    if (files.length === 0) return;\n\n    setSelectedFiles(files);\n    \n    // Create previews\n    const newPreviews: string[] = [];\n    files.forEach(file => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        newPreviews.push(e.target?.result as string);\n        if (newPreviews.length === files.length) {\n          setPreviews(newPreviews);\n          setStep(2);\n        }\n      };\n      reader.readAsDataURL(file);\n    });\n  };\n\n  const handleShare = async () => {\n    if (selectedFiles.length === 0) return;\n\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      selectedFiles.forEach(file => {\n        formData.append('images', file);\n      });\n      formData.append('caption', caption);\n      formData.append('location', location);\n\n      const response = await fetch('/api/posts', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        addPost(data.post);\n        handleClose();\n      } else {\n        console.error('Failed to create post');\n      }\n    } catch (error) {\n      console.error('Error creating post:', error);\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex min-h-full items-center justify-center p-4 text-center sm:p-0\">\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\" onClick={handleClose} />\n        \n        <div className=\"relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n            <button\n              onClick={handleClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"w-6 h-6\" />\n            </button>\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              {step === 1 ? 'Create new post' : step === 2 ? 'Edit' : 'Share'}\n            </h3>\n            {step === 2 && (\n              <button\n                onClick={() => setStep(3)}\n                className=\"text-blue-500 font-semibold hover:text-blue-600\"\n              >\n                Next\n              </button>\n            )}\n            {step === 3 && (\n              <button\n                onClick={handleShare}\n                disabled={isUploading}\n                className=\"text-blue-500 font-semibold hover:text-blue-600 disabled:opacity-50\"\n              >\n                {isUploading ? 'Sharing...' : 'Share'}\n              </button>\n            )}\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4\">\n            {step === 1 && (\n              <div className=\"text-center py-12\">\n                <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\n                  Drag photos and videos here\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-500\">\n                  Select from your computer\n                </p>\n                <button\n                  onClick={() => fileInputRef.current?.click()}\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n                >\n                  Select from computer\n                </button>\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  multiple\n                  accept=\"image/*,video/*\"\n                  onChange={handleFileSelect}\n                  className=\"hidden\"\n                />\n              </div>\n            )}\n\n            {step === 2 && previews.length > 0 && (\n              <div className=\"space-y-4\">\n                <div className=\"aspect-square bg-gray-100 rounded-lg overflow-hidden\">\n                  <img\n                    src={previews[0]}\n                    alt=\"Preview\"\n                    className=\"w-full h-full object-cover\"\n                  />\n                </div>\n                {previews.length > 1 && (\n                  <div className=\"flex space-x-2 overflow-x-auto\">\n                    {previews.slice(1).map((preview, index) => (\n                      <img\n                        key={index}\n                        src={preview}\n                        alt={`Preview ${index + 2}`}\n                        className=\"w-16 h-16 object-cover rounded-lg flex-shrink-0\"\n                      />\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {step === 3 && (\n              <div className=\"space-y-4\">\n                <div className=\"flex space-x-4\">\n                  <div className=\"w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0\">\n                    <img\n                      src={previews[0]}\n                      alt=\"Preview\"\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n                  <div className=\"flex-1 space-y-3\">\n                    <textarea\n                      placeholder=\"Write a caption...\"\n                      value={caption}\n                      onChange={(e) => setCaption(e.target.value)}\n                      rows={4}\n                      className=\"w-full text-sm border-none outline-none resize-none placeholder-gray-400\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Add location\"\n                      value={location}\n                      onChange={(e) => setLocation(e.target.value)}\n                      className=\"w-full text-sm border-none outline-none placeholder-gray-400\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,qCAAqC;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,cAAc;QAClB,kBAAkB;QAClB,QAAQ;QACR,iBAAiB,EAAE;QACnB,YAAY,EAAE;QACd,WAAW;QACX,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAC7C,IAAI,MAAM,MAAM,KAAK,GAAG;QAExB,iBAAiB;QAEjB,kBAAkB;QAClB,MAAM,cAAwB,EAAE;QAChC,MAAM,OAAO,CAAC,CAAA;YACZ,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,YAAY,IAAI,CAAC,EAAE,MAAM,EAAE;gBAC3B,IAAI,YAAY,MAAM,KAAK,MAAM,MAAM,EAAE;oBACvC,YAAY;oBACZ,QAAQ;gBACV;YACF;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,cAAc,MAAM,KAAK,GAAG;QAEhC,eAAe;QACf,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,cAAc,OAAO,CAAC,CAAA;gBACpB,SAAS,MAAM,CAAC,UAAU;YAC5B;YACA,SAAS,MAAM,CAAC,WAAW;YAC3B,SAAS,MAAM,CAAC,YAAY;YAE5B,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;gBACjB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;oBAA0D,SAAS;;;;;;8BAElF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAG,WAAU;8CACX,SAAS,IAAI,oBAAoB,SAAS,IAAI,SAAS;;;;;;gCAEzD,SAAS,mBACR,8OAAC;oCACC,SAAS,IAAM,QAAQ;oCACvB,WAAU;8CACX;;;;;;gCAIF,SAAS,mBACR,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,cAAc,eAAe;;;;;;;;;;;;sCAMpC,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,mBACR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CACC,SAAS,IAAM,aAAa,OAAO,EAAE;4CACrC,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;;;;;;;gCAKf,SAAS,KAAK,SAAS,MAAM,GAAG,mBAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAK,QAAQ,CAAC,EAAE;gDAChB,KAAI;gDACJ,WAAU;;;;;;;;;;;wCAGb,SAAS,MAAM,GAAG,mBACjB,8OAAC;4CAAI,WAAU;sDACZ,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC;oDAEC,KAAK;oDACL,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG;oDAC3B,WAAU;mDAHL;;;;;;;;;;;;;;;;gCAWhB,SAAS,mBACR,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK,QAAQ,CAAC,EAAE;oDAChB,KAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC1C,MAAM;wDACN,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Sidebar from './Sidebar';\nimport MobileNavigation from './MobileNavigation';\nimport CreatePostModal from '@/components/modals/CreatePostModal';\nimport { useStore } from '@/store/useStore';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { showCreatePost } = useStore();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Desktop Sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile Navigation */}\n      <div className=\"lg:hidden\">\n        <MobileNavigation />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"lg:pl-64\">\n        <main className=\"py-4 lg:py-8\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n\n      {/* Modals */}\n      {showCreatePost && <CreatePostModal />}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYe,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAElC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;0BAIV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;YAMN,gCAAkB,8OAAC,+IAAA,CAAA,UAAe;;;;;;;;;;;AAGzC", "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/feed/Stories.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { PlusIcon } from '@heroicons/react/24/outline';\nimport { useStore } from '@/store/useStore';\n\ninterface StoryUser {\n  _id: string;\n  username: string;\n  profilePicture?: string;\n  hasStory: boolean;\n  isViewed: boolean;\n}\n\nexport default function Stories() {\n  const { data: session } = useSession();\n  const { stories, setStories } = useStore();\n  const [storyUsers, setStoryUsers] = useState<StoryUser[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    fetchStories();\n  }, []);\n\n  const fetchStories = async () => {\n    try {\n      const response = await fetch('/api/stories');\n      if (response.ok) {\n        const data = await response.json();\n        setStoryUsers(data.users);\n        setStories(data.stories);\n      }\n    } catch (error) {\n      console.error('Error fetching stories:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCreateStory = () => {\n    // TODO: Implement story creation\n    console.log('Create story clicked');\n  };\n\n  const handleViewStory = (userId: string) => {\n    // TODO: Implement story viewer\n    console.log('View story for user:', userId);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"bg-white rounded-lg p-4 post-shadow\">\n        <div className=\"flex space-x-4 overflow-x-auto scrollbar-hide\">\n          {[...Array(6)].map((_, i) => (\n            <div key={i} className=\"flex flex-col items-center space-y-2 flex-shrink-0\">\n              <div className=\"w-16 h-16 bg-gray-300 rounded-full animate-pulse\"></div>\n              <div className=\"w-12 h-3 bg-gray-300 rounded animate-pulse\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 post-shadow\">\n      <div className=\"flex space-x-4 overflow-x-auto scrollbar-hide\">\n        {/* Add Story Button */}\n        <div className=\"flex flex-col items-center space-y-2 flex-shrink-0\">\n          <button\n            onClick={handleCreateStory}\n            className=\"relative w-16 h-16 rounded-full border-2 border-gray-300 border-dashed flex items-center justify-center hover:border-gray-400 transition-colors\"\n          >\n            <PlusIcon className=\"w-6 h-6 text-gray-400\" />\n          </button>\n          <span className=\"text-xs text-gray-600 text-center max-w-[60px] truncate\">\n            Your story\n          </span>\n        </div>\n\n        {/* Story Users */}\n        {storyUsers.map((user) => (\n          <div\n            key={user._id}\n            className=\"flex flex-col items-center space-y-2 flex-shrink-0 cursor-pointer\"\n            onClick={() => handleViewStory(user._id)}\n          >\n            <div\n              className={`story-ring ${\n                user.hasStory && !user.isViewed\n                  ? 'instagram-gradient'\n                  : user.hasStory\n                  ? 'bg-gray-300'\n                  : 'bg-transparent'\n              }`}\n            >\n              <div className=\"story-inner\">\n                <div className=\"w-14 h-14 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n                  {user.profilePicture ? (\n                    <img\n                      className=\"w-full h-full object-cover\"\n                      src={user.profilePicture}\n                      alt={user.username}\n                    />\n                  ) : (\n                    <span className=\"text-lg font-semibold text-gray-600\">\n                      {user.username.charAt(0).toUpperCase()}\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n            <span className=\"text-xs text-gray-600 text-center max-w-[60px] truncate\">\n              {user.username}\n            </span>\n          </div>\n        ))}\n\n        {/* Show message if no stories */}\n        {storyUsers.length === 0 && (\n          <div className=\"flex-1 flex items-center justify-center py-8\">\n            <p className=\"text-gray-500 text-sm\">\n              No stories to show. Follow people to see their stories here.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc,KAAK,KAAK;gBACxB,WAAW,KAAK,OAAO;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,iCAAiC;QACjC,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,+BAA+B;QAC/B,QAAQ,GAAG,CAAC,wBAAwB;IACtC;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wBAAY,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;uBAFP;;;;;;;;;;;;;;;IAQpB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAEtB,8OAAC;4BAAK,WAAU;sCAA0D;;;;;;;;;;;;gBAM3E,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,gBAAgB,KAAK,GAAG;;0CAEvC,8OAAC;gCACC,WAAW,CAAC,WAAW,EACrB,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,GAC3B,uBACA,KAAK,QAAQ,GACb,gBACA,kBACJ;0CAEF,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,KAAK,cAAc,iBAClB,8OAAC;4CACC,WAAU;4CACV,KAAK,KAAK,cAAc;4CACxB,KAAK,KAAK,QAAQ;;;;;iEAGpB,8OAAC;4CAAK,WAAU;sDACb,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;0CAM9C,8OAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ;;;;;;;uBA9BX,KAAK,GAAG;;;;;gBAoChB,WAAW,MAAM,KAAK,mBACrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/feed/PostCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport Link from 'next/link';\nimport { formatDistanceToNow } from 'date-fns';\nimport {\n  HeartIcon,\n  ChatBubbleOvalLeftIcon,\n  PaperAirplaneIcon,\n  BookmarkIcon,\n  EllipsisHorizontalIcon,\n} from '@heroicons/react/24/outline';\nimport {\n  HeartIcon as HeartIconSolid,\n  BookmarkIcon as BookmarkIconSolid,\n} from '@heroicons/react/24/solid';\nimport { Post } from '@/models/Post';\nimport { useStore } from '@/store/useStore';\n\ninterface PostCardProps {\n  post: Post;\n}\n\nexport default function PostCard({ post }: PostCardProps) {\n  const { data: session } = useSession();\n  const { updatePost } = useStore();\n  const [isLiked, setIsLiked] = useState(\n    post.likes.some(like => like.toString() === session?.user?.id)\n  );\n  const [isSaved, setIsSaved] = useState(false);\n  const [showComments, setShowComments] = useState(false);\n  const [comment, setComment] = useState('');\n  const [isSubmittingComment, setIsSubmittingComment] = useState(false);\n\n  const handleLike = async () => {\n    try {\n      const response = await fetch(`/api/posts/${post._id}/like`, {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setIsLiked(data.isLiked);\n        updatePost(post._id!.toString(), {\n          likes: data.likes,\n          likesCount: data.likesCount,\n        });\n      }\n    } catch (error) {\n      console.error('Error liking post:', error);\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      const response = await fetch(`/api/posts/${post._id}/save`, {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setIsSaved(data.isSaved);\n      }\n    } catch (error) {\n      console.error('Error saving post:', error);\n    }\n  };\n\n  const handleComment = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!comment.trim()) return;\n\n    setIsSubmittingComment(true);\n    try {\n      const response = await fetch(`/api/posts/${post._id}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ content: comment }),\n      });\n\n      if (response.ok) {\n        setComment('');\n        // Refresh comments or update state\n      }\n    } catch (error) {\n      console.error('Error posting comment:', error);\n    } finally {\n      setIsSubmittingComment(false);\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg post-shadow overflow-hidden\">\n      {/* Post Header */}\n      <div className=\"flex items-center justify-between p-4\">\n        <div className=\"flex items-center space-x-3\">\n          <Link href={`/profile/${post.userId}`}>\n            <div className=\"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center\">\n              <span className=\"text-sm font-semibold text-gray-600\">\n                U\n              </span>\n            </div>\n          </Link>\n          <div>\n            <Link href={`/profile/${post.userId}`}>\n              <h3 className=\"font-semibold text-gray-900 hover:underline\">\n                username\n              </h3>\n            </Link>\n            {post.location && (\n              <p className=\"text-sm text-gray-500\">{post.location}</p>\n            )}\n          </div>\n        </div>\n        <button className=\"text-gray-400 hover:text-gray-600\">\n          <EllipsisHorizontalIcon className=\"w-5 h-5\" />\n        </button>\n      </div>\n\n      {/* Post Images */}\n      {post.images && post.images.length > 0 && (\n        <div className=\"relative\">\n          <img\n            className=\"w-full h-96 object-cover\"\n            src={post.images[0]}\n            alt=\"Post content\"\n          />\n          {post.images.length > 1 && (\n            <div className=\"absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded-full text-sm\">\n              1/{post.images.length}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Post Actions */}\n      <div className=\"p-4\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={handleLike}\n              className={`transition-colors ${\n                isLiked ? 'text-red-500' : 'text-gray-700 hover:text-gray-900'\n              }`}\n            >\n              {isLiked ? (\n                <HeartIconSolid className=\"w-6 h-6\" />\n              ) : (\n                <HeartIcon className=\"w-6 h-6\" />\n              )}\n            </button>\n            <button\n              onClick={() => setShowComments(!showComments)}\n              className=\"text-gray-700 hover:text-gray-900\"\n            >\n              <ChatBubbleOvalLeftIcon className=\"w-6 h-6\" />\n            </button>\n            <button className=\"text-gray-700 hover:text-gray-900\">\n              <PaperAirplaneIcon className=\"w-6 h-6\" />\n            </button>\n          </div>\n          <button\n            onClick={handleSave}\n            className={`transition-colors ${\n              isSaved ? 'text-gray-900' : 'text-gray-700 hover:text-gray-900'\n            }`}\n          >\n            {isSaved ? (\n              <BookmarkIconSolid className=\"w-6 h-6\" />\n            ) : (\n              <BookmarkIcon className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Likes Count */}\n        {post.likesCount > 0 && (\n          <p className=\"font-semibold text-gray-900 mb-2\">\n            {post.likesCount} {post.likesCount === 1 ? 'like' : 'likes'}\n          </p>\n        )}\n\n        {/* Caption */}\n        {post.caption && (\n          <div className=\"mb-2\">\n            <span className=\"font-semibold text-gray-900 mr-2\">username</span>\n            <span className=\"text-gray-900\">{post.caption}</span>\n          </div>\n        )}\n\n        {/* Comments Count */}\n        {post.commentsCount > 0 && (\n          <button\n            onClick={() => setShowComments(!showComments)}\n            className=\"text-gray-500 text-sm mb-2 hover:underline\"\n          >\n            View all {post.commentsCount} comments\n          </button>\n        )}\n\n        {/* Timestamp */}\n        <p className=\"text-gray-400 text-xs uppercase\">\n          {formatDistanceToNow(new Date(post.createdAt), { addSuffix: true })}\n        </p>\n\n        {/* Comment Form */}\n        <form onSubmit={handleComment} className=\"mt-3 pt-3 border-t border-gray-100\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0\">\n              {session?.user?.image ? (\n                <img\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                  src={session.user.image}\n                  alt=\"\"\n                />\n              ) : (\n                <span className=\"text-xs font-semibold text-gray-600\">\n                  {session?.user?.name?.charAt(0).toUpperCase()}\n                </span>\n              )}\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Add a comment...\"\n              value={comment}\n              onChange={(e) => setComment(e.target.value)}\n              className=\"flex-1 text-sm border-none outline-none placeholder-gray-400\"\n            />\n            {comment.trim() && (\n              <button\n                type=\"submit\"\n                disabled={isSubmittingComment}\n                className=\"text-blue-500 font-semibold text-sm hover:text-blue-600 disabled:opacity-50\"\n              >\n                Post\n              </button>\n            )}\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AAKA;AAlBA;;;;;;;;;AAwBe,SAAS,SAAS,EAAE,IAAI,EAAiB;IACtD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC9B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,OAAO,SAAS,MAAM;IAE7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC1D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO;gBACvB,WAAW,KAAK,GAAG,CAAE,QAAQ,IAAI;oBAC/B,OAAO,KAAK,KAAK;oBACjB,YAAY,KAAK,UAAU;gBAC7B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC1D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,EAAE,cAAc;QAChB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,uBAAuB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAQ;YAC1C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;YACX,mCAAmC;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,SAAS,EAAE,KAAK,MAAM,EAAE;0CACnC,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;;;;;0CAK1D,8OAAC;;kDACC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,SAAS,EAAE,KAAK,MAAM,EAAE;kDACnC,cAAA,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;;;;;;oCAI7D,KAAK,QAAQ,kBACZ,8OAAC;wCAAE,WAAU;kDAAyB,KAAK,QAAQ;;;;;;;;;;;;;;;;;;kCAIzD,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC,2OAAA,CAAA,yBAAsB;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKrC,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,KAAK,KAAK,MAAM,CAAC,EAAE;wBACnB,KAAI;;;;;;oBAEL,KAAK,MAAM,CAAC,MAAM,GAAG,mBACpB,8OAAC;wBAAI,WAAU;;4BAA0F;4BACpG,KAAK,MAAM,CAAC,MAAM;;;;;;;;;;;;;0BAO7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,kBAAkB,EAC5B,UAAU,iBAAiB,qCAC3B;kDAED,wBACC,8OAAC,+MAAA,CAAA,YAAc;4CAAC,WAAU;;;;;iEAE1B,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAGzB,8OAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDAEV,cAAA,8OAAC,2OAAA,CAAA,yBAAsB;4CAAC,WAAU;;;;;;;;;;;kDAEpC,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,iOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjC,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAC,kBAAkB,EAC5B,UAAU,kBAAkB,qCAC5B;0CAED,wBACC,8OAAC,qNAAA,CAAA,eAAiB;oCAAC,WAAU;;;;;yDAE7B,8OAAC,uNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAM7B,KAAK,UAAU,GAAG,mBACjB,8OAAC;wBAAE,WAAU;;4BACV,KAAK,UAAU;4BAAC;4BAAE,KAAK,UAAU,KAAK,IAAI,SAAS;;;;;;;oBAKvD,KAAK,OAAO,kBACX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAmC;;;;;;0CACnD,8OAAC;gCAAK,WAAU;0CAAiB,KAAK,OAAO;;;;;;;;;;;;oBAKhD,KAAK,aAAa,GAAG,mBACpB,8OAAC;wBACC,SAAS,IAAM,gBAAgB,CAAC;wBAChC,WAAU;;4BACX;4BACW,KAAK,aAAa;4BAAC;;;;;;;kCAKjC,8OAAC;wBAAE,WAAU;kCACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;4BAAE,WAAW;wBAAK;;;;;;kCAInE,8OAAC;wBAAK,UAAU;wBAAe,WAAU;kCACvC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,sBACd,8OAAC;wCACC,WAAU;wCACV,KAAK,QAAQ,IAAI,CAAC,KAAK;wCACvB,KAAI;;;;;6DAGN,8OAAC;wCAAK,WAAU;kDACb,SAAS,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;8CAItC,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,WAAU;;;;;;gCAEX,QAAQ,IAAI,oBACX,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/feed/SuggestedUsers.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { UserProfile } from '@/models/User';\n\nexport default function SuggestedUsers() {\n  const [suggestedUsers, setSuggestedUsers] = useState<UserProfile[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    fetchSuggestedUsers();\n  }, []);\n\n  const fetchSuggestedUsers = async () => {\n    try {\n      const response = await fetch('/api/users/suggested');\n      if (response.ok) {\n        const data = await response.json();\n        setSuggestedUsers(data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching suggested users:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleFollow = async (userId: string) => {\n    try {\n      const response = await fetch(`/api/users/${userId}/follow`, {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        // Remove user from suggestions after following\n        setSuggestedUsers(prev => prev.filter(user => user._id.toString() !== userId));\n      }\n    } catch (error) {\n      console.error('Error following user:', error);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"bg-white rounded-lg p-4 post-shadow\">\n        <h3 className=\"font-semibold text-gray-900 mb-4\">Suggested for you</h3>\n        <div className=\"space-y-3\">\n          {[...Array(5)].map((_, i) => (\n            <div key={i} className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-gray-300 rounded-full animate-pulse\"></div>\n                <div className=\"space-y-1\">\n                  <div className=\"w-20 h-4 bg-gray-300 rounded animate-pulse\"></div>\n                  <div className=\"w-16 h-3 bg-gray-300 rounded animate-pulse\"></div>\n                </div>\n              </div>\n              <div className=\"w-16 h-6 bg-gray-300 rounded animate-pulse\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (suggestedUsers.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 post-shadow\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"font-semibold text-gray-900\">Suggested for you</h3>\n        <Link\n          href=\"/explore/people\"\n          className=\"text-sm text-blue-500 hover:text-blue-600 font-medium\"\n        >\n          See All\n        </Link>\n      </div>\n      \n      <div className=\"space-y-3\">\n        {suggestedUsers.slice(0, 5).map((user) => (\n          <div key={user._id.toString()} className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <Link href={`/profile/${user.username}`}>\n                <div className=\"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n                  {user.profilePicture ? (\n                    <img\n                      className=\"w-full h-full object-cover\"\n                      src={user.profilePicture}\n                      alt={user.username}\n                    />\n                  ) : (\n                    <span className=\"text-sm font-semibold text-gray-600\">\n                      {user.username.charAt(0).toUpperCase()}\n                    </span>\n                  )}\n                </div>\n              </Link>\n              <div className=\"min-w-0 flex-1\">\n                <Link href={`/profile/${user.username}`}>\n                  <h4 className=\"font-semibold text-gray-900 text-sm hover:underline truncate\">\n                    {user.username}\n                  </h4>\n                </Link>\n                <p className=\"text-xs text-gray-500 truncate\">\n                  {user.fullName}\n                </p>\n                {user.followersCount > 0 && (\n                  <p className=\"text-xs text-gray-400\">\n                    {user.followersCount} followers\n                  </p>\n                )}\n              </div>\n            </div>\n            <button\n              onClick={() => handleFollow(user._id.toString())}\n              className=\"text-blue-500 hover:text-blue-600 font-semibold text-sm\"\n            >\n              Follow\n            </button>\n          </div>\n        ))}\n      </div>\n      \n      {/* Footer */}\n      <div className=\"mt-6 pt-4 border-t border-gray-100\">\n        <div className=\"text-xs text-gray-400 space-y-1\">\n          <div className=\"flex flex-wrap gap-2\">\n            <Link href=\"/about\" className=\"hover:underline\">About</Link>\n            <Link href=\"/help\" className=\"hover:underline\">Help</Link>\n            <Link href=\"/press\" className=\"hover:underline\">Press</Link>\n            <Link href=\"/api\" className=\"hover:underline\">API</Link>\n            <Link href=\"/jobs\" className=\"hover:underline\">Jobs</Link>\n            <Link href=\"/privacy\" className=\"hover:underline\">Privacy</Link>\n            <Link href=\"/terms\" className=\"hover:underline\">Terms</Link>\n          </div>\n          <p className=\"mt-2\">© 2024 Instagram Clone</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,kBAAkB,KAAK,KAAK;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,EAAE;gBAC1D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,+CAA+C;gBAC/C,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,CAAC,QAAQ,OAAO;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAmC;;;;;;8BACjD,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAI,WAAU;;;;;;;2BARP;;;;;;;;;;;;;;;;IAcpB;IAEA,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;0BACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC/B,8OAAC;wBAA8B,WAAU;;0CACvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,SAAS,EAAE,KAAK,QAAQ,EAAE;kDACrC,cAAA,8OAAC;4CAAI,WAAU;sDACZ,KAAK,cAAc,iBAClB,8OAAC;gDACC,WAAU;gDACV,KAAK,KAAK,cAAc;gDACxB,KAAK,KAAK,QAAQ;;;;;qEAGpB,8OAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,SAAS,EAAE,KAAK,QAAQ,EAAE;0DACrC,cAAA,8OAAC;oDAAG,WAAU;8DACX,KAAK,QAAQ;;;;;;;;;;;0DAGlB,8OAAC;gDAAE,WAAU;0DACV,KAAK,QAAQ;;;;;;4CAEf,KAAK,cAAc,GAAG,mBACrB,8OAAC;gDAAE,WAAU;;oDACV,KAAK,cAAc;oDAAC;;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCACC,SAAS,IAAM,aAAa,KAAK,GAAG,CAAC,QAAQ;gCAC7C,WAAU;0CACX;;;;;;;uBApCO,KAAK,GAAG,CAAC,QAAQ;;;;;;;;;;0BA4C/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAkB;;;;;;8CAChD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAkB;;;;;;8CAChD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAkB;;;;;;8CAC9C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAkB;;;;;;8CAClD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAkB;;;;;;;;;;;;sCAElD,8OAAC;4BAAE,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAK9B", "debugId": null}}, {"offset": {"line": 2099, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/feed/Feed.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport Stories from './Stories';\nimport PostCard from './PostCard';\nimport SuggestedUsers from './SuggestedUsers';\nimport { useStore } from '@/store/useStore';\nimport { Post } from '@/models/Post';\n\nexport default function Feed() {\n  const { data: session } = useSession();\n  const { posts, setPosts, isLoading, setIsLoading } = useStore();\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchPosts();\n  }, []);\n\n  const fetchPosts = async () => {\n    try {\n      setIsLoading(true);\n      const response = await fetch('/api/posts/feed');\n      if (response.ok) {\n        const data = await response.json();\n        setPosts(data.posts);\n      } else {\n        setError('Failed to load posts');\n      }\n    } catch (error) {\n      setError('An error occurred while loading posts');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"max-w-2xl mx-auto\">\n        <div className=\"animate-pulse space-y-6\">\n          {/* Stories skeleton */}\n          <div className=\"bg-white rounded-lg p-4 post-shadow\">\n            <div className=\"flex space-x-4\">\n              {[...Array(5)].map((_, i) => (\n                <div key={i} className=\"flex flex-col items-center space-y-2\">\n                  <div className=\"w-16 h-16 bg-gray-300 rounded-full\"></div>\n                  <div className=\"w-12 h-3 bg-gray-300 rounded\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n          \n          {/* Posts skeleton */}\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"bg-white rounded-lg post-shadow\">\n              <div className=\"p-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-10 h-10 bg-gray-300 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <div className=\"w-24 h-4 bg-gray-300 rounded\"></div>\n                  </div>\n                </div>\n              </div>\n              <div className=\"w-full h-96 bg-gray-300\"></div>\n              <div className=\"p-4 space-y-3\">\n                <div className=\"flex space-x-4\">\n                  <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                  <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                  <div className=\"w-6 h-6 bg-gray-300 rounded\"></div>\n                </div>\n                <div className=\"w-20 h-4 bg-gray-300 rounded\"></div>\n                <div className=\"w-full h-4 bg-gray-300 rounded\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Main Feed */}\n        <div className=\"lg:col-span-2\">\n          <div className=\"space-y-6\">\n            {/* Stories */}\n            <Stories />\n            \n            {/* Posts */}\n            {error ? (\n              <div className=\"bg-white rounded-lg p-8 text-center post-shadow\">\n                <p className=\"text-gray-500\">{error}</p>\n                <button\n                  onClick={fetchPosts}\n                  className=\"mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600\"\n                >\n                  Try Again\n                </button>\n              </div>\n            ) : posts.length === 0 ? (\n              <div className=\"bg-white rounded-lg p-8 text-center post-shadow\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                  Welcome to Instagram!\n                </h3>\n                <p className=\"text-gray-500 mb-4\">\n                  Start following people to see their posts in your feed.\n                </p>\n                <button\n                  onClick={() => window.location.href = '/explore'}\n                  className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600\"\n                >\n                  Explore People\n                </button>\n              </div>\n            ) : (\n              posts.map((post) => (\n                <PostCard key={post._id?.toString()} post={post} />\n              ))\n            )}\n          </div>\n        </div>\n        \n        {/* Sidebar */}\n        <div className=\"hidden lg:block\">\n          <div className=\"sticky top-8 space-y-6\">\n            {/* User Profile Card */}\n            {session?.user && (\n              <div className=\"bg-white rounded-lg p-4 post-shadow\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-14 h-14 rounded-full bg-gray-300 flex items-center justify-center\">\n                    {session.user.image ? (\n                      <img\n                        className=\"w-14 h-14 rounded-full object-cover\"\n                        src={session.user.image}\n                        alt={session.user.name || ''}\n                      />\n                    ) : (\n                      <span className=\"text-xl font-semibold text-gray-600\">\n                        {session.user.name?.charAt(0).toUpperCase()}\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-semibold text-gray-900\">\n                      {session.user.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-500\">\n                      @{(session.user as any).username || 'username'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            {/* Suggested Users */}\n            <SuggestedUsers />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;YACrB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;mCAFP;;;;;;;;;;;;;;;oBASf;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4BAAY,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAIrB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAjBT;;;;;;;;;;;;;;;;IAwBpB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,qIAAA,CAAA,UAAO;;;;;4BAGP,sBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAiB;;;;;;kDAC9B,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;uCAID,MAAM,MAAM,KAAK,kBACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wCACtC,WAAU;kDACX;;;;;;;;;;;uCAKH,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC,sIAAA,CAAA,UAAQ;oCAA4B,MAAM;mCAA5B,KAAK,GAAG,EAAE;;;;;;;;;;;;;;;;8BAOjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BAEZ,SAAS,sBACR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,CAAC,KAAK,iBACjB,8OAAC;gDACC,WAAU;gDACV,KAAK,QAAQ,IAAI,CAAC,KAAK;gDACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;;;;;qEAG5B,8OAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG;;;;;;;;;;;sDAIpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI,CAAC,IAAI;;;;;;8DAEpB,8OAAC;oDAAE,WAAU;;wDAAwB;wDAChC,QAAQ,IAAI,CAAS,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAQ9C,8OAAC,4IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 2502, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useEffect } from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport Feed from '@/components/feed/Feed';\n\nexport default function Home() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (status === 'loading') return; // Still loading\n    if (!session) router.push('/auth/signin'); // Not signed in\n  }, [session, status, router]);\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"></div>\n      </div>\n    );\n  }\n\n  if (!session) {\n    return null; // Will redirect to sign in\n  }\n\n  return (\n    <MainLayout>\n      <Feed />\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW,QAAQ,gBAAgB;QAClD,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,iBAAiB,gBAAgB;IAC7D,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,2BAA2B;IAC1C;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACT,cAAA,8OAAC,kIAAA,CAAA,UAAI;;;;;;;;;;AAGX", "debugId": null}}]}