{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/lib/socket.ts"], "sourcesContent": ["import { io, Socket } from 'socket.io-client';\n\nclass SocketManager {\n  private socket: Socket | null = null;\n  private static instance: SocketManager;\n\n  private constructor() {}\n\n  static getInstance(): SocketManager {\n    if (!SocketManager.instance) {\n      SocketManager.instance = new SocketManager();\n    }\n    return SocketManager.instance;\n  }\n\n  connect(userId: string): Socket {\n    if (!this.socket) {\n      this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {\n        auth: {\n          userId,\n        },\n      });\n\n      this.socket.on('connect', () => {\n        console.log('Connected to socket server');\n      });\n\n      this.socket.on('disconnect', () => {\n        console.log('Disconnected from socket server');\n      });\n    }\n\n    return this.socket;\n  }\n\n  disconnect(): void {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n    }\n  }\n\n  getSocket(): Socket | null {\n    return this.socket;\n  }\n\n  emit(event: string, data: any): void {\n    if (this.socket) {\n      this.socket.emit(event, data);\n    }\n  }\n\n  on(event: string, callback: (data: any) => void): void {\n    if (this.socket) {\n      this.socket.on(event, callback);\n    }\n  }\n\n  off(event: string, callback?: (data: any) => void): void {\n    if (this.socket) {\n      this.socket.off(event, callback);\n    }\n  }\n}\n\nexport default SocketManager;\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM;IACI,SAAwB,KAAK;IACrC,OAAe,SAAwB;IAEvC,aAAsB,CAAC;IAEvB,OAAO,cAA6B;QAClC,IAAI,CAAC,cAAc,QAAQ,EAAE;YAC3B,cAAc,QAAQ,GAAG,IAAI;QAC/B;QACA,OAAO,cAAc,QAAQ;IAC/B;IAEA,QAAQ,MAAc,EAAU;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,6DAAsC,yBAAyB;gBAC9E,MAAM;oBACJ;gBACF;YACF;YAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;gBACxB,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;gBAC3B,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;IACF;IAEA,YAA2B;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,KAAK,KAAa,EAAE,IAAS,EAAQ;QACnC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;QAC1B;IACF;IAEA,GAAG,KAAa,EAAE,QAA6B,EAAQ;QACrD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;IACF;IAEA,IAAI,KAAa,EAAE,QAA8B,EAAQ;QACvD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;QACzB;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/instagramclone/src/components/Providers.tsx"], "sourcesContent": ["'use client';\n\nimport { SessionProvider } from 'next-auth/react';\nimport { useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport SocketManager from '@/lib/socket';\n\nfunction SocketProvider({ children }: { children: React.ReactNode }) {\n  const { data: session } = useSession();\n\n  useEffect(() => {\n    if (session?.user?.id) {\n      const socketManager = SocketManager.getInstance();\n      const socket = socketManager.connect(session.user.id);\n      \n      socket.emit('authenticate', session.user.id);\n\n      return () => {\n        socketManager.disconnect();\n      };\n    }\n  }, [session]);\n\n  return <>{children}</>;\n}\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <SessionProvider>\n      <SocketProvider>\n        {children}\n      </SocketProvider>\n    </SessionProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;;AAOA,SAAS,eAAe,EAAE,QAAQ,EAAiC;IACjE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,IAAI;YACrB,MAAM,gBAAgB,oHAAA,CAAA,UAAa,CAAC,WAAW;YAC/C,MAAM,SAAS,cAAc,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE;YAEpD,OAAO,IAAI,CAAC,gBAAgB,QAAQ,IAAI,CAAC,EAAE;YAE3C,OAAO;gBACL,cAAc,UAAU;YAC1B;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,qBAAO;kBAAG;;AACZ;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,8OAAC,8IAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT", "debugId": null}}]}