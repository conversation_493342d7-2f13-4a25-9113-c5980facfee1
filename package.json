{"name": "instagramclone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "socket": "node socket-server.js"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@next-auth/mongodb-adapter": "^1.1.3", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "next": "15.3.2", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}