import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const db = await getDatabase();
    const userId = new ObjectId(session.user.id);

    // Get current user's following list
    const currentUser = await db.collection('users').findOne({ _id: userId });
    const following = currentUser?.following || [];

    // Include current user in the list
    const userIds = [userId, ...following];

    // Get active stories (not expired)
    const now = new Date();
    const stories = await db
      .collection('stories')
      .find({
        userId: { $in: userIds },
        expiresAt: { $gt: now },
      })
      .sort({ createdAt: -1 })
      .toArray();

    // Group stories by user
    const storiesByUser = stories.reduce((acc, story) => {
      const userIdStr = story.userId.toString();
      if (!acc[userIdStr]) {
        acc[userIdStr] = [];
      }
      acc[userIdStr].push(story);
      return acc;
    }, {} as Record<string, any[]>);

    // Get user details and check if stories are viewed
    const users = await Promise.all(
      Object.keys(storiesByUser).map(async (userIdStr) => {
        const user = await db.collection('users').findOne(
          { _id: new ObjectId(userIdStr) },
          { projection: { username: 1, profilePicture: 1 } }
        );

        const userStories = storiesByUser[userIdStr];
        const hasUnviewedStory = userStories.some(story => 
          !story.viewers.some((viewer: ObjectId) => viewer.equals(userId))
        );

        return {
          _id: userIdStr,
          username: user?.username || 'Unknown',
          profilePicture: user?.profilePicture,
          hasStory: true,
          isViewed: !hasUnviewedStory,
        };
      })
    );

    return NextResponse.json({
      users: users.filter(user => user.username !== 'Unknown'),
      stories: storiesByUser,
    });
  } catch (error) {
    console.error('Error fetching stories:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
