# Instagram Clone

A complete Instagram clone built with Next.js 14, MongoDB Atlas, and WebRTC for real-time communication.

## Features

### ✅ Implemented Core Features
- **Authentication**: User registration, login, logout with NextAuth.js
- **Posts**: Create, view, like, comment on posts
- **Stories**: View stories (creation coming soon)
- **Feed**: Personalized feed based on following
- **User Profiles**: View user profiles and suggested users
- **Real-time Messaging**: Socket.io integration ready
- **Responsive Design**: Mobile-first design with Tailwind CSS

### 🚧 Coming Soon
- **Direct Messages**: Text, media, voice notes
- **Voice/Video Calls**: WebRTC implementation
- **Stories Creation**: Upload and share stories
- **Explore Page**: Discover new content
- **Reels**: Short video content
- **Search**: Find users and content
- **Notifications**: Real-time notifications
- **Profile Editing**: Update profile information

## Tech Stack

- **Frontend & Backend**: Next.js 14 (App Router)
- **Database**: MongoDB Atlas
- **Authentication**: NextAuth.js
- **Real-time**: Socket.io + WebRTC
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Image Processing**: Sharp
- **Icons**: Heroicons

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd instagramclone
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Variables
Create a `.env.local` file in the root directory and add:

```env
# MongoDB Atlas
MONGODB_URI=mongodb+srv://your-username:<EMAIL>/instagramclone?retryWrites=true&w=majority

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here-make-it-long-and-random

# JWT
JWT_SECRET=your-jwt-secret-key-here-make-it-long-and-random

# Socket.io
SOCKET_PORT=3001
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
```

### 4. MongoDB Atlas Setup
1. Create a MongoDB Atlas account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Create a database user
4. Get your connection string and replace the MONGODB_URI in .env.local
5. Whitelist your IP address

### 5. Generate Secret Keys
Generate random secret keys for NEXTAUTH_SECRET and JWT_SECRET:
```bash
openssl rand -base64 32
```

### 6. Run the Application
```bash
# Start the Next.js development server
npm run dev

# In another terminal, start the Socket.io server
npm run socket
```

The application will be available at:
- **Web App**: http://localhost:3000
- **Socket.io Server**: http://localhost:3001

## Project Structure

```
instagramclone/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── api/            # API routes
│   │   │   ├── auth/       # Authentication endpoints
│   │   │   ├── posts/      # Posts CRUD operations
│   │   │   ├── users/      # User management
│   │   │   ├── conversations/ # Messaging endpoints
│   │   │   ├── stories/    # Stories endpoints
│   │   │   └── search/     # Search functionality
│   │   ├── auth/           # Authentication pages
│   │   ├── messages/       # Direct messages page
│   │   ├── profile/        # User profile page
│   │   ├── search/         # Search page
│   │   └── globals.css     # Global styles
│   ├── components/         # React components
│   │   ├── feed/          # Feed-related components
│   │   ├── layout/        # Layout components
│   │   ├── messaging/     # Chat components
│   │   ├── modals/        # Modal components
│   │   └── profile/       # Profile components
│   ├── lib/               # Utility libraries
│   │   ├── mongodb.ts     # Database connection
│   │   ├── auth.ts        # NextAuth configuration
│   │   ├── socket.ts      # Socket.io client
│   │   └── webrtc.ts      # WebRTC utilities
│   ├── models/            # TypeScript interfaces
│   │   ├── User.ts        # User data models
│   │   ├── Post.ts        # Post data models
│   │   └── Message.ts     # Message data models
│   └── store/             # Zustand store
│       └── useStore.ts    # Global state management
├── socket-server.js       # Socket.io server
├── .env.local            # Environment variables
└── package.json
```

## Features Implemented

### ✅ Core Features
1. **User Authentication**
   - Registration with email, username, password
   - Login/logout functionality
   - Session management with NextAuth.js
   - Protected routes

2. **Posts System**
   - Create posts with multiple images
   - Like/unlike posts
   - Comment on posts
   - View personalized feed
   - Image processing with Sharp

3. **User Profiles**
   - View user profiles
   - Profile statistics (posts, followers, following)
   - Profile tabs (posts, reels, saved, tagged)

4. **Stories System**
   - View stories from followed users
   - Story expiration (24 hours)
   - Story viewer status

5. **Search Functionality**
   - Search users by username/name
   - Search posts by hashtags/content
   - Recent searches

6. **Real-time Messaging Infrastructure**
   - Socket.io server setup
   - Conversation management
   - Message sending/receiving
   - Typing indicators
   - Online/offline status

7. **WebRTC Integration**
   - Voice/video call infrastructure
   - Peer-to-peer connection setup
   - Call management utilities

### 🚧 Ready for Implementation
1. **Direct Messages**
   - Text messaging (infrastructure ready)
   - Media sharing
   - Voice notes
   - Message reactions

2. **Voice/Video Calls**
   - WebRTC implementation ready
   - Call UI components needed
   - Screen sharing capability

3. **Stories Creation**
   - Upload story media
   - Story editing tools
   - Story privacy settings

4. **Advanced Features**
   - Explore page
   - Reels creation/viewing
   - Notifications system
   - Follow requests for private accounts

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/[...nextauth]` - NextAuth.js endpoints

### Posts
- `GET /api/posts/feed` - Get user feed
- `POST /api/posts` - Create new post
- `POST /api/posts/[postId]/like` - Like/unlike post
- `GET /api/posts/[postId]/comments` - Get post comments
- `POST /api/posts/[postId]/comments` - Add comment

### Users
- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update user profile
- `GET /api/users/suggested` - Get suggested users

### Messaging
- `GET /api/conversations` - Get user conversations
- `POST /api/conversations` - Create new conversation
- `GET /api/conversations/[id]/messages` - Get conversation messages

### Search
- `GET /api/search?q=query` - Search users and posts

### Stories
- `GET /api/stories` - Get active stories

## Database Schema

### Users Collection
```javascript
{
  _id: ObjectId,
  username: String (unique),
  email: String (unique),
  password: String (hashed),
  fullName: String,
  bio: String,
  profilePicture: String,
  isPrivate: Boolean,
  followers: [ObjectId],
  following: [ObjectId],
  postsCount: Number,
  isVerified: Boolean,
  isOnline: Boolean,
  lastSeen: Date,
  createdAt: Date,
  updatedAt: Date
}
```

### Posts Collection
```javascript
{
  _id: ObjectId,
  userId: ObjectId,
  caption: String,
  images: [String],
  videos: [String],
  location: String,
  tags: [String],
  mentions: [ObjectId],
  likes: [ObjectId],
  likesCount: Number,
  commentsCount: Number,
  sharesCount: Number,
  isArchived: Boolean,
  commentsDisabled: Boolean,
  hideLikeCount: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### Messages Collection
```javascript
{
  _id: ObjectId,
  conversationId: ObjectId,
  senderId: ObjectId,
  content: String,
  messageType: String, // 'text', 'image', 'video', 'audio', 'call'
  media: String,
  reactions: [{ userId: ObjectId, emoji: String, createdAt: Date }],
  isEdited: Boolean,
  isDeleted: Boolean,
  readBy: [{ userId: ObjectId, readAt: Date }],
  createdAt: Date,
  updatedAt: Date
}
```

### Conversations Collection
```javascript
{
  _id: ObjectId,
  participants: [ObjectId],
  isGroup: Boolean,
  groupName: String,
  groupImage: String,
  groupAdmins: [ObjectId],
  lastMessage: ObjectId,
  lastMessageAt: Date,
  unreadCount: { [userId]: Number },
  createdAt: Date,
  updatedAt: Date
}
```

## Socket.io Events

### Connection Events
- `authenticate` - User authentication
- `user_online` - User comes online
- `user_offline` - User goes offline

### Messaging Events
- `send_message` - Send a message
- `new_message` - Receive a message
- `mark_as_read` - Mark message as read
- `message_read` - Message read confirmation
- `typing_start` - User starts typing
- `typing_stop` - User stops typing
- `user_typing` - Typing indicator

### Call Events
- `call_user` - Initiate a call
- `incoming_call` - Receive call invitation
- `answer_call` - Answer a call
- `call_answered` - Call answered confirmation
- `reject_call` - Reject a call
- `call_rejected` - Call rejected confirmation
- `end_call` - End a call
- `call_ended` - Call ended confirmation
- `ice_candidate` - WebRTC ICE candidate exchange

## Next Steps

1. **Set up MongoDB Atlas**
   - Create account and cluster
   - Update connection string in .env.local
   - Test database connection

2. **Complete Messaging UI**
   - Implement message components
   - Add media sharing
   - Test real-time messaging

3. **Implement Video Calls**
   - Create call UI components
   - Test WebRTC functionality
   - Add call history

4. **Add Story Creation**
   - Story upload interface
   - Story editing tools
   - Story viewer

5. **Enhance Features**
   - Explore page
   - Reels functionality
   - Push notifications
   - Advanced search filters

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational purposes only.
