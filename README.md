# Instagram Clone

A complete Instagram clone built with Next.js 14, MongoDB Atlas, and WebRTC for real-time communication.

## Features

### ✅ Implemented Core Features
- **Authentication**: User registration, login, logout with NextAuth.js
- **Posts**: Create, view, like, comment on posts
- **Stories**: View stories (creation coming soon)
- **Feed**: Personalized feed based on following
- **User Profiles**: View user profiles and suggested users
- **Real-time Messaging**: Socket.io integration ready
- **Responsive Design**: Mobile-first design with Tailwind CSS

### 🚧 Coming Soon
- **Direct Messages**: Text, media, voice notes
- **Voice/Video Calls**: WebRTC implementation
- **Stories Creation**: Upload and share stories
- **Explore Page**: Discover new content
- **Reels**: Short video content
- **Search**: Find users and content
- **Notifications**: Real-time notifications
- **Profile Editing**: Update profile information

## Tech Stack

- **Frontend & Backend**: Next.js 14 (App Router)
- **Database**: MongoDB Atlas
- **Authentication**: NextAuth.js
- **Real-time**: Socket.io + WebRTC
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Image Processing**: Sharp
- **Icons**: Heroicons

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd instagramclone
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Variables
Create a `.env.local` file in the root directory and add:

```env
# MongoDB Atlas
MONGODB_URI=mongodb+srv://your-username:<EMAIL>/instagramclone?retryWrites=true&w=majority

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here-make-it-long-and-random

# JWT
JWT_SECRET=your-jwt-secret-key-here-make-it-long-and-random

# Socket.io
SOCKET_PORT=3001
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
```

### 4. MongoDB Atlas Setup
1. Create a MongoDB Atlas account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Create a database user
4. Get your connection string and replace the MONGODB_URI in .env.local
5. Whitelist your IP address

### 5. Generate Secret Keys
Generate random secret keys for NEXTAUTH_SECRET and JWT_SECRET:
```bash
openssl rand -base64 32
```

### 6. Run the Application
```bash
# Start the Next.js development server
npm run dev

# In another terminal, start the Socket.io server
npm run socket
```

The application will be available at:
- **Web App**: http://localhost:3000
- **Socket.io Server**: http://localhost:3001
